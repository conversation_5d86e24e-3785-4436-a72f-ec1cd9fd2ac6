syntax = "proto3";

package qax;
import "cline/common.proto";
option java_package = "bot.qax.proto";
option java_multiple_files = true;

// QaxTodoService provides methods for managing Qax Todo lists
service QaxTodoService {
  // Updates the complete todo list for the current task
  rpc updateQaxTodoList(QaxUpdateTodoListRequest) returns (QaxTodoListResponse);

  // Deletes a specific todo item by ID
  rpc deleteQaxTodoItem(QaxDeleteTodoItemRequest) returns (QaxTodoListResponse);

  // Adds a new todo item to the current task
  rpc addQaxTodoItem(QaxAddTodoItemRequest) returns (QaxTodoListResponse);

  // Updates the status of a specific todo item
  rpc updateQaxTodoItemStatus(QaxUpdateTodoItemStatusRequest) returns (QaxTodoListResponse);

  // Gets the current todo list for the active task
  rpc getQaxTodoList(cline.EmptyRequest) returns (QaxTodoListResponse);
}

// Qax Todo status enumeration
enum QaxTodoStatus {
  QAX_TODO_STATUS_UNSPECIFIED = 0;
  QAX_TODO_STATUS_PENDING = 1;
  QAX_TODO_STATUS_IN_PROGRESS = 2;
  QAX_TODO_STATUS_COMPLETED = 3;
}

// Qax Todo item message
message QaxTodoItem {
  string id = 1;
  string content = 2;
  QaxTodoStatus status = 3;
}

// Request to update the complete todo list
message QaxUpdateTodoListRequest {
  cline.Metadata metadata = 1;
  repeated QaxTodoItem todos = 2;
}

// Request to delete a todo item
message QaxDeleteTodoItemRequest {
  cline.Metadata metadata = 1;
  string todo_id = 2;
}

// Request to add a new todo item
message QaxAddTodoItemRequest {
  cline.Metadata metadata = 1;
  string content = 2;
  QaxTodoStatus status = 3;
}

// Request to update todo item status
message QaxUpdateTodoItemStatusRequest {
  cline.Metadata metadata = 1;
  string todo_id = 2;
  QaxTodoStatus status = 3;
}

// Response containing todo list data
message QaxTodoListResponse {
  repeated QaxTodoItem todos = 1;
  QaxTodoStats stats = 2;
}

// Todo list statistics
message QaxTodoStats {
  int32 total = 1;
  int32 completed = 2;
  int32 in_progress = 3;
  int32 pending = 4;
}
