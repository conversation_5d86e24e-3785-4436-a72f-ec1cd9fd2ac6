/**
 * Simplified ContextGatherer test that focuses on core functionality
 * without complex mocking to avoid TypeScript compilation issues
 */

import { ContextGatherer } from "../ContextGatherer"

export class SimpleContextTest {
	/**
	 * Main test runner - validates core context gathering functionality
	 */
	public static async runTests(): Promise<void> {
		console.log("🧪 [SimpleContextTest] Starting core functionality tests...")
		console.log("=".repeat(80))

		const tests = [
			SimpleContextTest.testImportQueryLoading,
			SimpleContextTest.testClipboardContentFiltering,
			SimpleContextTest.testFunctionSignatureExtraction,
			SimpleContextTest.testContextStructure,
		]

		let passed = 0
		const total = tests.length

		for (const test of tests) {
			try {
				const result = await test()
				if (result.passed) {
					console.log(`✅ ${result.name}: ${result.details}`)
					passed++
				} else {
					console.log(`❌ ${result.name}: ${result.details}`)
				}
			} catch (error) {
				console.log(`❌ ${test.name}: Error - ${error}`)
			}
		}

		console.log("\n" + "=".repeat(80))
		console.log(`📊 RESULTS: ${passed}/${total} tests passed (${((passed / total) * 100).toFixed(1)}%)`)
		console.log(passed === total ? "🎉 ALL TESTS PASSED!" : "⚠️  Some tests failed")
		console.log("=".repeat(80))
	}

	/**
	 * Test 1: Import query loading functionality
	 */
	private static async testImportQueryLoading(): Promise<{ name: string; passed: boolean; details: string }> {
		try {
			// Test the import query loading mechanism
			const { getImportQuery } = await import("../../tree-sitter/languageParser")

			const testFiles = [
				"test.js",
				"test.ts",
				"test.py",
				"test.rs",
				"test.go",
				"test.java",
				"test.cpp",
				"test.c",
				"test.cs",
				"test.rb",
				"test.php",
				"test.swift",
				"test.kt",
			]

			let queriesLoaded = 0
			for (const file of testFiles) {
				try {
					const query = await getImportQuery(file)
					if (query) {
						queriesLoaded++
					}
				} catch (error) {
					// Some queries might not be available, that's ok
				}
			}

			const passed = queriesLoaded >= 10 // Most languages should work
			return {
				name: "Import Query Loading",
				passed,
				details: `Loaded ${queriesLoaded}/${testFiles.length} import queries`,
			}
		} catch (error) {
			return {
				name: "Import Query Loading",
				passed: false,
				details: `Error: ${error}`,
			}
		}
	}

	/**
	 * Test 2: Clipboard content filtering
	 */
	private static async testClipboardContentFiltering(): Promise<{ name: string; passed: boolean; details: string }> {
		try {
			const gatherer = new ContextGatherer()

			// Test the clipboard content filtering logic
			const testCases = [
				{ content: "function test() { return 42; }", shouldInclude: true },
				{ content: "Hello world", shouldInclude: false },
				{ content: "", shouldInclude: false },
				{ content: "const x = 5; // comment", shouldInclude: true },
			]

			let correctFiltering = 0
			for (const testCase of testCases) {
				// Access the private method through any casting (for testing)
				const isLikelyCode = (gatherer as any)._isLikelyCodeOrMeaningfulText(testCase.content)
				if (isLikelyCode === testCase.shouldInclude) {
					correctFiltering++
				}
			}

			const passed = correctFiltering === testCases.length
			return {
				name: "Clipboard Content Filtering",
				passed,
				details: `${correctFiltering}/${testCases.length} filtering decisions correct`,
			}
		} catch (error) {
			return {
				name: "Clipboard Content Filtering",
				passed: false,
				details: `Error: ${error}`,
			}
		}
	}

	/**
	 * Test 3: Function signature extraction
	 */
	private static async testFunctionSignatureExtraction(): Promise<{ name: string; passed: boolean; details: string }> {
		try {
			const gatherer = new ContextGatherer()

			const testDefinitions = [
				{
					filepath: "/test/utils.js",
					content: "function helper(x, y) { return x + y; }",
					source: "import",
				},
				{
					filepath: "/test/components.js",
					content: "const Button = () => <button>Click</button>",
					source: "recent_visit",
				},
			]

			let signaturesExtracted = 0
			for (const def of testDefinitions) {
				try {
					// Access the private method for testing
					const signature = (gatherer as any)._getFunctionSignature(def)
					if (signature && signature.includes(def.filepath)) {
						signaturesExtracted++
					}
				} catch (error) {
					// Some extractions might fail, that's ok for testing
				}
			}

			const passed = signaturesExtracted >= 1
			return {
				name: "Function Signature Extraction",
				passed,
				details: `${signaturesExtracted}/${testDefinitions.length} signatures extracted`,
			}
		} catch (error) {
			return {
				name: "Function Signature Extraction",
				passed: false,
				details: `Error: ${error}`,
			}
		}
	}

	/**
	 * Test 4: Context structure validation
	 */
	private static async testContextStructure(): Promise<{ name: string; passed: boolean; details: string }> {
		try {
			// Test that the CodeContext interface structure is correct
			const sampleContext = {
				currentLine: "const result = helper()",
				precedingLines: ["import { helper } from './utils'", ""],
				followingLines: ["return result", "}"],
				imports: ["helper"],
				definitions: [
					{
						filepath: "/test/utils.js",
						content: "function helper() { return 42; }",
						range: { start: { line: 0, character: 0 }, end: { line: 0, character: 30 } },
						source: "import",
					},
				],
				clipboardContent: "const utils = { helper: () => 42 }",
			}

			// Validate structure
			const hasRequiredFields =
				typeof sampleContext.currentLine === "string" &&
				Array.isArray(sampleContext.precedingLines) &&
				Array.isArray(sampleContext.followingLines) &&
				Array.isArray(sampleContext.imports) &&
				Array.isArray(sampleContext.definitions) &&
				typeof sampleContext.clipboardContent === "string"

			const hasValidDefinitions = sampleContext.definitions.every(
				(def) =>
					typeof def.filepath === "string" &&
					typeof def.content === "string" &&
					typeof def.source === "string" &&
					def.range &&
					typeof def.range.start.line === "number",
			)

			const passed = hasRequiredFields && hasValidDefinitions
			return {
				name: "Context Structure Validation",
				passed,
				details: `Required fields: ${hasRequiredFields}, Valid definitions: ${hasValidDefinitions}`,
			}
		} catch (error) {
			return {
				name: "Context Structure Validation",
				passed: false,
				details: `Error: ${error}`,
			}
		}
	}

	/**
	 * Test the tree-sitter import parsing functionality
	 */
	public static async testTreeSitterImportParsing(): Promise<void> {
		console.log("\n🌳 [SimpleContextTest] Testing Tree-sitter Import Parsing...")
		console.log("-".repeat(50))

		const testCodes = [
			{
				name: "JavaScript",
				code: "import React from 'react'\nimport { useState } from 'react'",
				language: "javascript",
			},
			{
				name: "Python",
				code: "import os\nfrom typing import List",
				language: "python",
			},
		]

		for (const testCode of testCodes) {
			try {
				console.log(`  Testing ${testCode.name} import parsing...`)

				// This would test the actual parsing if we had proper document mocking
				// For now, just validate that the query loading works
				const { getImportQuery } = await import("../../tree-sitter/languageParser")
				const query = await getImportQuery(`test.${testCode.language === "javascript" ? "js" : "py"}`)

				if (query) {
					console.log(`  ✅ ${testCode.name}: Import query loaded successfully`)
				} else {
					console.log(`  ❌ ${testCode.name}: Failed to load import query`)
				}
			} catch (error) {
				console.log(`  ❌ ${testCode.name}: Error - ${error}`)
			}
		}
	}
}

// Export function for easy testing
export async function runSimpleContextTests(): Promise<void> {
	await SimpleContextTest.runTests()
	await SimpleContextTest.testTreeSitterImportParsing()
}
