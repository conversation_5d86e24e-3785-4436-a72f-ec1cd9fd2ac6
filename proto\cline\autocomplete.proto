syntax = "proto3";

package cline;
import "cline/common.proto";
option java_package = "bot.cline.proto";
option java_multiple_files = true;

// Autocomplete settings message
message AutocompleteSettings {
  bool enabled = 1;
  optional string provider = 2;
  optional string api_key = 3;
  optional string api_base_url = 4;
  optional string model_id = 5;
  optional int32 max_tokens = 6;
  optional double temperature = 7;
  optional int64 request_timeout_ms = 8;
  optional bool use_prompt_cache = 9;
  optional string custom_headers = 10; // JSON string for custom headers
  optional int32 debounce_ms = 11;
  optional AutocompleteFilterSettings filter = 12;
  optional FimSettings fim = 13;
}

// Autocomplete filter settings
message AutocompleteFilterSettings {
  bool enabled = 1;
  bool filter_whitespace_only = 2;
  bool filter_repeated_content = 3;
  repeated string filter_strings = 4;
}

// FIM (Fill in the Middle) specific settings
message FimSettings {
  optional string api_key = 1;
  optional string base_url = 2;
  optional string model_id = 3;
}

// Message for syncing autocomplete state
message SyncAutocompleteStateRequest {
  Metadata metadata = 1;
  bool sending_disabled = 2;
  int64 timestamp = 3;
}
