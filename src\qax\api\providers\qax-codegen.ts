/**
 * Qax Codegen 平台 API Handler
 * <AUTHOR>
 */

import { Anthropic } from "@anthropic-ai/sdk"
import { ModelInfo, OpenAiCompatibleModelInfo, openAiModelInfoSaneDefaults } from "@shared/api"
import { getQaxCodegenBaseUrl, getQaxDefaultModel } from "@shared/qax"
import OpenAI from "openai"
import type { ChatCompletionReasoningEffort } from "openai/resources/chat/completions"
import { ApiHandler } from "../../../core/api"
import { withRetry } from "../../../core/api/retry"
import { convertToOpenAiMessages } from "../../../core/api/transform/openai-format"
import { convertToR1Format } from "../../../core/api/transform/r1-format"
import { ApiStream } from "../../../core/api/transform/stream"
import { QaxAccountService } from "../../services/account/QaxAccountService"
import { validateJWTToken } from "../../utils/jwt"

interface QaxCodegenHandlerOptions {
	qaxCodegenToken?: string
	qaxCodegenModelId?: string
	qaxCodegenModelInfo?: OpenAiCompatibleModelInfo
	qaxCodegenHeaders?: Record<string, string>
	reasoningEffort?: string
}

export class QaxCodegenHandler implements ApiHandler {
	private options: QaxCodegenHandlerOptions
	private client: OpenAI | undefined
	private qaxAccountService = QaxAccountService.getInstance()

	constructor(options: QaxCodegenHandlerOptions) {
		this.options = options
	}

	private async ensureClient(): Promise<OpenAI> {
		const token = await this.getValidJwtToken()

		if (!this.client) {
			const baseURL = getQaxCodegenBaseUrl()

			try {
				this.client = new OpenAI({
					baseURL,
					apiKey: token,
					defaultHeaders: this.options.qaxCodegenHeaders,
				})
			} catch (error: any) {
				throw new Error(`Error creating QAX Codegen client: ${error.message}`)
			}
		}

		// Ensure the client is always using the latest JWT token
		this.client.apiKey = token
		return this.client
	}

	/**
	 * 获取当前有效的 JWT token
	 * 从 QaxAuthService 获取登录后的 JWT token
	 */
	private async getValidJwtToken(): Promise<string> {
		// 优先使用登录后的 JWT token
		const authToken = await this.qaxAccountService.getAuthService().getAuthToken()

		if (authToken) {
			try {
				// 使用统一的 JWT 验证工具
				validateJWTToken(authToken)
				return authToken
			} catch (error) {
				console.error("[QAX Codegen Provider] Auth token validation failed:", error)
			}
		}

		// 如果没有登录 token，尝试使用配置中的 token（向后兼容）
		const configToken = this.options.qaxCodegenToken

		if (configToken) {
			try {
				validateJWTToken(configToken)
				return configToken
			} catch (error) {
				console.error("[QAX Codegen Provider] Config token validation failed:", error)
			}
		}

		throw new Error("QAX Codegen JWT token is required. Please login to QAX Account first.")
	}

	// 注释掉未使用的方法，保留代码以便将来参考
	/*
	 * 处理 API 返回的 usage 信息
	 * 目前 Qax Codegen 接口不返回 usage 信息，但保留此方法以便将来支持
	 *
	 * @private
	 * @param _info - 模型信息（未使用）
	 * @param usage - OpenAI 兼容的 usage 信息
	 *
	private async *yieldUsage(_info: ModelInfo, usage: OpenAI.Completions.CompletionUsage | undefined): ApiStream {
		if (!usage) {return;}

		const inputTokens = usage.prompt_tokens || 0
		const outputTokens = usage.completion_tokens || 0
		const cacheReadTokens = usage.prompt_tokens_details?.cached_tokens || 0
		const cacheWriteTokens = 0

		yield {
			type: "usage",
			inputTokens: inputTokens,
			outputTokens: outputTokens,
			cacheReadTokens: cacheReadTokens,
			cacheWriteTokens: cacheWriteTokens,
		}
	}
	*/

	/**
	 * 处理 usage 信息的辅助方法
	 * 由于 Qax Codegen 接口不返回 usage 信息，这里提供估算的 usage
	 */
	private async *yieldEstimatedUsage(
		openAiMessages: OpenAI.Chat.Completions.ChatCompletionMessageParam[],
		totalOutputTokens: number,
	): ApiStream {
		// 简单估算输入 token 数量
		const estimatedInputTokens = Math.ceil(JSON.stringify(openAiMessages).length / 4)
		yield {
			type: "usage",
			inputTokens: estimatedInputTokens,
			outputTokens: totalOutputTokens,
			cacheReadTokens: 0,
			cacheWriteTokens: 0,
		}
	}

	@withRetry()
	async *createMessage(systemPrompt: string, messages: Anthropic.Messages.MessageParam[]): ApiStream {
		try {
			// Validate request before proceeding (following Cline 3.18.12 pattern)
			await this.qaxAccountService.validateRequest()

			const client = await this.ensureClient()
			const model = this.getModel()

			// 检测 DeepSeek R1 模型：包含 "deepseek-reasoner" 或者 "deepseek" + "r1" 的组合
			const isDeepseekReasoner = /deepseek-reasoner|deepseek.*r1/.test(model.id.toLowerCase())
			const isR1FormatRequired = this.options.qaxCodegenModelInfo?.isR1FormatRequired ?? isDeepseekReasoner
			const isReasoningModelFamily = model.id.includes("o1") || model.id.includes("o3") || model.id.includes("o4")

			let openAiMessages: OpenAI.Chat.Completions.ChatCompletionMessageParam[]
			let temperature: number | undefined =
				this.options.qaxCodegenModelInfo?.temperature ?? openAiModelInfoSaneDefaults.temperature
			// 如果 temperature 是 0，则设置为 0.1
			if (temperature === 0) {
				temperature = 0.6
			}
			let maxTokens: number | undefined
			let reasoningEffort: ChatCompletionReasoningEffort | undefined

			if (this.options.qaxCodegenModelInfo?.maxTokens && this.options.qaxCodegenModelInfo.maxTokens > 0) {
				maxTokens = Number(this.options.qaxCodegenModelInfo.maxTokens)
			} else {
				maxTokens = model.info.maxTokens || 8192
			}

			if (isR1FormatRequired) {
				// 使用 R1 格式转换
				openAiMessages = convertToR1Format([{ role: "user", content: systemPrompt }, ...messages])
			} else {
				// 使用标准 OpenAI 格式转换
				openAiMessages = [{ role: "system", content: systemPrompt }, ...convertToOpenAiMessages(messages)]
			}

			if (isReasoningModelFamily) {
				openAiMessages = [{ role: "developer", content: systemPrompt }, ...convertToOpenAiMessages(messages)]
				temperature = undefined // does not support temperature
				reasoningEffort = (this.options.reasoningEffort as ChatCompletionReasoningEffort) || "medium"
			}

			const stream = await client.chat.completions.create({
				model: model.id,
				messages: openAiMessages,
				temperature,
				max_tokens: maxTokens,
				reasoning_effort: reasoningEffort,
				stream: true,
				// Qax Codegen 接口不支持 usage，移除 include_usage 选项
				// stream_options: { include_usage: true },
			})

			let totalOutputTokens = 0

			for await (const chunk of stream) {
				// 检查流错误
				if ("error" in chunk) {
					const error = chunk.error as any
					console.error(`QAX Codegen API Error: ${error?.code} - ${error?.message}`)
					throw new Error(`QAX Codegen API Error ${error?.code || "Unknown"}: ${error?.message || "Unknown error"}`)
				}

				// 检查中间流错误
				const choice = chunk.choices?.[0]
				if ((choice?.finish_reason as string) === "error") {
					const choiceWithError = choice as any
					if (choiceWithError.error) {
						const error = choiceWithError.error
						console.error(`QAX Codegen Mid-Stream Error: ${error.code || error.type || "Unknown"} - ${error.message}`)
						throw new Error(
							`QAX Codegen Mid-Stream Error: ${error.code || error.type || "Unknown"} - ${error.message}`,
						)
					} else {
						throw new Error(
							"QAX Codegen Mid-Stream Error: Stream terminated with error status but no error details provided",
						)
					}
				}

				const delta = choice?.delta
				if (delta?.content) {
					// 简单估算输出 token 数量（如果没有 usage 信息）
					totalOutputTokens += Math.ceil(delta.content.length / 4)
					yield {
						type: "text",
						text: delta.content,
					}
				}

				if (delta && "reasoning_content" in delta && delta.reasoning_content) {
					yield {
						type: "reasoning",
						reasoning: (delta.reasoning_content as string | undefined) || "",
					}
				}

				// Qax Codegen 接口不返回 usage 信息，跳过处理
				// 注意：如果将来接口支持返回 usage，可以取消下面的注释并使用类似其他提供者的模式
				/*
				if (chunk.usage) {
					yield* this.yieldUsage(model.info, chunk.usage)
				}
				*/
			}

			// 由于 Qax Codegen 接口不返回 usage 信息，始终提供一个估算的 usage
			yield* this.yieldEstimatedUsage(openAiMessages, totalOutputTokens)
		} catch (error) {
			// Enhanced error handling following Cline 3.18.12 pattern
			if (error.code === "ERR_BAD_REQUEST" || error.status === 401) {
				throw new Error("Unauthorized: Please check your QAX Codegen authentication and try again.")
			} else if (error.code === "insufficient_credits" || error.status === 402) {
				throw new Error(error.error ? JSON.stringify(error.error) : "Insufficient credits or unknown error.")
			}
			console.error("QAX Codegen API Error:", error)
			throw error instanceof Error ? error : new Error(`${error}`)
		}
	}

	getModel(): { id: string; info: ModelInfo } {
		return {
			id: this.options.qaxCodegenModelId ?? getQaxDefaultModel(),
			info: this.options.qaxCodegenModelInfo ?? openAiModelInfoSaneDefaults,
		}
	}
}
