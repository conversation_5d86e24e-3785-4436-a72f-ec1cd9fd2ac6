#!/usr/bin/env node

/**
 * 完整的上下文提取测试程序
 * 测试增强的extractIntelligentContext函数的所有场景
 * 这个测试程序验证inter-declaration检测逻辑的正确性
 */

const fs = require("fs")
const path = require("path")

// 测试文件内容
const TEST_FILES = {
	javascript: `// File header comment
import { something } from 'module';

/**
 * First function with documentation
 */
function firstFunction() {
    const x = 1;
    return x + 2;
}

// Comment between functions
// Another comment line

/**
 * Second function
 */
function secondFunction(param) {
    if (param > 0) {
        return param * 2;
    }
    return 0;
}

// End of file comment`,

	typescript: `interface User {
    id: number;
    name: string;
}

// Interface and class separator comment

class UserService {
    private users: User[] = [];
    
    addUser(user: User): void {
        this.users.push(user);
    }
}

// Another comment block
// Multiple lines of comments

export function processUsers(users: User[]): number {
    return users.length;
}`,

	python: `"""Module docstring"""
import os
import sys

def first_function():
    """First function docstring"""
    x = 1
    return x + 2

# Comment between functions
# Multiple comment lines

class MyClass:
    """Class docstring"""
    
    def __init__(self):
        self.value = 0
    
    def method(self, param):
        return param * 2

# End comment`,
}

// 测试用例定义
const TEST_CASES = [
	{
		name: "JS - 光标在第一个函数内部",
		fileContent: TEST_FILES.javascript,
		cursorLine: 7,
		cursorChar: 10,
		expectedStrategy: "meaningful-parent",
		description: "光标在firstFunction函数体内",
	},
	{
		name: "JS - 光标在函数之间的注释区域",
		fileContent: TEST_FILES.javascript,
		cursorLine: 11,
		cursorChar: 5,
		expectedStrategy: "inter-declaration",
		description: "光标在firstFunction和secondFunction之间的注释中",
	},
	{
		name: "JS - 光标在第二个函数内部",
		fileContent: TEST_FILES.javascript,
		cursorLine: 17,
		cursorChar: 15,
		expectedStrategy: "meaningful-parent",
		description: "光标在secondFunction函数体内",
	},
	{
		name: "JS - 光标在文件开头",
		fileContent: TEST_FILES.javascript,
		cursorLine: 0,
		cursorChar: 0,
		expectedStrategy: "inter-declaration",
		description: "光标在文件最开始位置",
	},
	{
		name: "JS - 光标在文件末尾",
		fileContent: TEST_FILES.javascript,
		cursorLine: 23,
		cursorChar: 0,
		expectedStrategy: "inter-declaration",
		description: "光标在文件末尾最后一个函数之后",
	},
	{
		name: "TS - 光标在接口内部",
		fileContent: TEST_FILES.typescript,
		cursorLine: 2,
		cursorChar: 10,
		expectedStrategy: "meaningful-parent",
		description: "光标在interface定义内部",
	},
	{
		name: "TS - 光标在接口和类之间",
		fileContent: TEST_FILES.typescript,
		cursorLine: 6,
		cursorChar: 0,
		expectedStrategy: "inter-declaration",
		description: "光标在interface和class之间的注释中",
	},
	{
		name: "TS - 光标在类方法内部",
		fileContent: TEST_FILES.typescript,
		cursorLine: 12,
		cursorChar: 20,
		expectedStrategy: "meaningful-parent",
		description: "光标在类方法内部",
	},
	{
		name: "PY - 光标在函数内部",
		fileContent: TEST_FILES.python,
		cursorLine: 7,
		cursorChar: 8,
		expectedStrategy: "meaningful-parent",
		description: "光标在Python函数内部",
	},
	{
		name: "PY - 光标在函数和类之间",
		fileContent: TEST_FILES.python,
		cursorLine: 11,
		cursorChar: 0,
		expectedStrategy: "inter-declaration",
		description: "光标在Python函数和类之间",
	},
]

// 更准确的上下文提取逻辑模拟
function simulateContextExtraction(fileContent, cursorLine, cursorChar, maxLines = 20) {
	const lines = fileContent.split("\n")
	const cursorOffset = lines.slice(0, cursorLine).join("\n").length + cursorChar

	// 更精确的声明检测
	const declarations = findDeclarations(fileContent)

	// 检查光标是否在某个声明的内部（不仅仅是开始位置）
	const insideDeclaration = declarations.find((decl) => {
		// 对于函数和类，检查光标是否在其主体内部
		if (decl.type === "function" || decl.type === "def") {
			// 找到函数体的开始（{或:之后）
			const bodyStart = findFunctionBodyStart(fileContent, decl.start)
			return cursorOffset > bodyStart && cursorOffset < decl.end
		} else if (decl.type === "class" || decl.type === "interface") {
			// 对于类和接口，检查是否在定义内部
			const bodyStart = findClassBodyStart(fileContent, decl.start)
			return cursorOffset > bodyStart && cursorOffset < decl.end
		}
		return false
	})

	if (insideDeclaration) {
		// 光标在声明内部 - meaningful-parent策略
		const contextStart = Math.max(0, insideDeclaration.start)
		const contextEnd = Math.min(fileContent.length, insideDeclaration.end)
		const contextCode = fileContent.slice(contextStart, contextEnd)

		return {
			contextCode,
			cursorLineInContext: cursorLine - fileContent.slice(0, contextStart).split("\n").length + 1,
			cursorCharInContext: cursorChar,
			usedFullFile: false,
			strategy: "meaningful-parent",
		}
	}

	// 检查是否在声明之间 - inter-declaration策略
	let precedingDeclaration = null
	let followingDeclaration = null

	for (const decl of declarations) {
		if (decl.end <= cursorOffset) {
			precedingDeclaration = decl
		} else if (decl.start > cursorOffset && !followingDeclaration) {
			followingDeclaration = decl
			break
		}
	}

	// 如果光标在声明之间，使用inter-declaration策略
	if (precedingDeclaration || followingDeclaration) {
		const contextStart = precedingDeclaration ? precedingDeclaration.start : 0
		const contextEnd = followingDeclaration ? followingDeclaration.end : fileContent.length
		const contextCode = fileContent.slice(contextStart, contextEnd)

		// 检查是否超过maxLines限制
		if (contextCode.split("\n").length > maxLines) {
			// 回退到context-window策略
			const windowStart = Math.max(0, cursorLine - Math.floor(maxLines / 2))
			const windowEnd = Math.min(lines.length, windowStart + maxLines)
			const windowCode = lines.slice(windowStart, windowEnd).join("\n")

			return {
				contextCode: windowCode,
				cursorLineInContext: cursorLine - windowStart,
				cursorCharInContext: cursorChar,
				usedFullFile: false,
				strategy: "context-window",
			}
		}

		return {
			contextCode,
			cursorLineInContext: cursorLine - fileContent.slice(0, contextStart).split("\n").length + 1,
			cursorCharInContext: cursorChar,
			usedFullFile: false,
			strategy: "inter-declaration",
		}
	}

	// 回退到context-window策略
	const windowStart = Math.max(0, cursorLine - Math.floor(maxLines / 2))
	const windowEnd = Math.min(lines.length, windowStart + maxLines)
	const windowCode = lines.slice(windowStart, windowEnd).join("\n")

	return {
		contextCode: windowCode,
		cursorLineInContext: cursorLine - windowStart,
		cursorCharInContext: cursorChar,
		usedFullFile: false,
		strategy: "context-window",
	}
}

// 辅助函数：查找所有声明
function findDeclarations(fileContent) {
	const functionMatches = [...fileContent.matchAll(/function\s+\w+\s*\([^)]*\)\s*\{/g)]
	const classMatches = [...fileContent.matchAll(/class\s+\w+[^{]*\{/g)]
	const interfaceMatches = [...fileContent.matchAll(/interface\s+\w+[^{]*\{/g)]
	const defMatches = [...fileContent.matchAll(/def\s+\w+\s*\([^)]*\):/g)]

	// 合并所有声明
	const declarations = [
		...functionMatches.map((m) => ({ type: "function", start: m.index, match: m[0] })),
		...classMatches.map((m) => ({ type: "class", start: m.index, match: m[0] })),
		...interfaceMatches.map((m) => ({ type: "interface", start: m.index, match: m[0] })),
		...defMatches.map((m) => ({ type: "def", start: m.index, match: m[0] })),
	].sort((a, b) => a.start - b.start)

	// 为每个声明找到结束位置
	declarations.forEach((decl, i) => {
		decl.end = findDeclarationEnd(fileContent, decl, declarations[i + 1])
	})

	return declarations
}

// 辅助函数：查找声明的结束位置
function findDeclarationEnd(fileContent, declaration, nextDeclaration) {
	if (declaration.type === "function") {
		// 对于JavaScript函数，找到匹配的}
		return findMatchingBrace(fileContent, declaration.start)
	} else if (declaration.type === "def") {
		// 对于Python函数，找到下一个同级声明或文件结束
		return findPythonFunctionEnd(fileContent, declaration.start, nextDeclaration)
	} else if (declaration.type === "class" || declaration.type === "interface") {
		// 对于类和接口，找到匹配的}
		return findMatchingBrace(fileContent, declaration.start)
	}
	return nextDeclaration ? nextDeclaration.start - 1 : fileContent.length
}

// 辅助函数：查找函数体开始位置
function findFunctionBodyStart(fileContent, declarationStart) {
	const openBrace = fileContent.indexOf("{", declarationStart)
	const colon = fileContent.indexOf(":", declarationStart)
	return Math.min(openBrace === -1 ? Infinity : openBrace, colon === -1 ? Infinity : colon) + 1
}

// 辅助函数：查找类体开始位置
function findClassBodyStart(fileContent, declarationStart) {
	const openBrace = fileContent.indexOf("{", declarationStart)
	return openBrace === -1 ? declarationStart : openBrace + 1
}

// 辅助函数：查找匹配的大括号
function findMatchingBrace(fileContent, start) {
	const openBrace = fileContent.indexOf("{", start)
	if (openBrace === -1) return fileContent.length

	let braceCount = 1
	let pos = openBrace + 1

	while (pos < fileContent.length && braceCount > 0) {
		if (fileContent[pos] === "{") {
			braceCount++
		} else if (fileContent[pos] === "}") {
			braceCount--
		}
		pos++
	}

	return pos
}

// 辅助函数：查找Python函数结束位置
function findPythonFunctionEnd(fileContent, start, nextDeclaration) {
	if (nextDeclaration) {
		return nextDeclaration.start - 1
	}

	// 简化：找到下一个def或class，或文件结束
	const lines = fileContent.split("\n")
	const startLine = fileContent.slice(0, start).split("\n").length - 1

	for (let i = startLine + 1; i < lines.length; i++) {
		if (lines[i].match(/^(def|class)\s+/)) {
			return fileContent.split("\n").slice(0, i).join("\n").length
		}
	}

	return fileContent.length
}

// 运行测试
function runTests() {
	console.log("🧪 开始上下文提取测试")
	console.log("=".repeat(60))

	let passedTests = 0
	const totalTests = TEST_CASES.length

	for (let i = 0; i < TEST_CASES.length; i++) {
		const testCase = TEST_CASES[i]
		console.log(`\n📋 测试 ${i + 1}/${totalTests}: ${testCase.name}`)
		console.log(`📝 描述: ${testCase.description}`)
		console.log(`📍 光标位置: 第${testCase.cursorLine + 1}行, 第${testCase.cursorChar + 1}字符`)

		try {
			// 执行上下文提取
			const result = simulateContextExtraction(testCase.fileContent, testCase.cursorLine, testCase.cursorChar, 20)

			// 验证结果
			console.log(`✅ 使用策略: ${result.strategy}`)
			console.log(`📊 上下文行数: ${result.contextCode.split("\n").length}`)
			console.log(`🎯 光标在上下文中的位置: 第${result.cursorLineInContext + 1}行, 第${result.cursorCharInContext + 1}字符`)
			console.log(`📄 使用完整文件: ${result.usedFullFile}`)

			// 检查策略是否匹配预期
			const strategyMatches = result.strategy === testCase.expectedStrategy
			if (strategyMatches) {
				console.log(`✅ 策略匹配预期: ${testCase.expectedStrategy}`)
				passedTests++
			} else {
				console.log(`❌ 策略不匹配! 预期: ${testCase.expectedStrategy}, 实际: ${result.strategy}`)
			}

			// 显示提取的上下文（前几行和后几行）
			const contextLines = result.contextCode.split("\n")
			console.log(`\n📄 提取的上下文 (${contextLines.length} 行):`)
			console.log("─".repeat(40))

			if (contextLines.length <= 8) {
				// 显示所有行
				contextLines.forEach((line, index) => {
					const marker = index === result.cursorLineInContext ? " 👈 光标" : ""
					console.log(`${String(index + 1).padStart(2)}: ${line}${marker}`)
				})
			} else {
				// 显示前3行和后3行
				for (let j = 0; j < 3; j++) {
					const marker = j === result.cursorLineInContext ? " 👈 光标" : ""
					console.log(`${String(j + 1).padStart(2)}: ${contextLines[j]}${marker}`)
				}
				console.log("   ...")
				for (let j = contextLines.length - 3; j < contextLines.length; j++) {
					const marker = j === result.cursorLineInContext ? " 👈 光标" : ""
					console.log(`${String(j + 1).padStart(2)}: ${contextLines[j]}${marker}`)
				}
			}

			console.log("─".repeat(40))
		} catch (error) {
			console.log(`❌ 测试失败，错误: ${error.message}`)
		}
	}

	console.log(`\n🏁 测试总结`)
	console.log("=".repeat(60))
	console.log(`✅ 通过: ${passedTests}/${totalTests}`)
	console.log(`❌ 失败: ${totalTests - passedTests}/${totalTests}`)
	console.log(`📊 成功率: ${Math.round((passedTests / totalTests) * 100)}%`)

	if (passedTests === totalTests) {
		console.log("🎉 所有测试都通过了!")
		return true
	} else {
		console.log("⚠️  部分测试失败，请检查上面的结果。")
		return false
	}
}

// 执行测试
if (require.main === module) {
	const success = runTests()
	process.exit(success ? 0 : 1)
}

module.exports = { runTests, simulateContextExtraction, TEST_CASES }
