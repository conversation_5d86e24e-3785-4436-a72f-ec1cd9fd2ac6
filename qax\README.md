# Qax Codegen 构建指南

本文档介绍如何构建 Qax Codegen 扩展的 VSIX 包。

## 📋 前置要求

- Node.js >= 18.0.0
- npm >= 8.0.0
- 已安装项目依赖：`npm run install:all`

## 🚀 快速开始

### 构建生产版本

```bash
npm run build:qax
# 或者明确指定环境
npm run build:qax -- --env=prod
```

### 构建开发版本

```bash
npm run build:qax -- --env=dev
```

### 构建测试版本

```bash
npm run build:qax -- --env=test
```

### 清理构建产物

```bash
npm run clean:qax
```

## 📁 目录结构

```
qax/
├── README.md                 # 本文档
├── package.json             # Qax 扩展配置
├── assets/                  # Qax 资源文件
│   └── icons/              # 图标文件
│       ├── icon.png        # PNG 图标
│       └── icon.svg        # SVG 图标
├── docs/                   # 文档文件
│   ├── README.md           # 用户文档
│   └── CHANGELOG.md        # 更新日志
├── webview-ui/             # WebView 界面
│   └── index.html          # Qax 专用 HTML 模板
├── scripts/                # 构建脚本
│   ├── build-qax.js        # 主构建脚本
│   ├── file-processor.js   # 文件处理器
│   └── qax-config.js       # 配置文件
└── cline-backup/           # 备份目录（构建时生成）
    └── 年月日时分/          # 时间戳目录
        └── 原始文件结构/    # 备份的原始文件
```

## 🔧 构建流程

### 1. 环境配置

构建脚本支持两种环境：

- **开发环境** (`dev`): 使用测试域名和调试配置
- **生产环境** (`prod`): 使用正式域名和生产配置

### 2. 构建步骤（优化后）

1. **自动配置 QAX_MODE**: 确保 `webview-ui/src/qax/utils/config.ts` 中的 `QAX_MODE = true`
2. **复制资源文件**: 将 Qax 专用的图标、文档等文件复制到项目根目录
3. **生成合并的 package.json**: 合并原始配置和 Qax 配置
4. **应用文本替换**: 使用简化的替换规则，减少误替换风险
5. **设置环境变量**: 配置构建环境变量
6. **执行构建**: 复用 Cline 原生构建流程（`npm run package`）
7. **生成 VSIX 包**: 使用 vsce 打包扩展
8. **清理临时文件**: 自动恢复所有原始文件

### 3. 备份机制

构建过程中会自动备份所有被修改的文件：

- **备份位置**: `qax/cline-backup/年月日时分/`
- **备份格式**: 保持原始目录结构
- **恢复机制**: 构建完成后自动恢复所有文件

## 📦 输出文件

构建成功后会生成：

- **VSIX 包**: `qax-codegen-v3.0.0.vsix`
- **构建产物**: `dist/` 目录
- **WebView 构建**: `webview-ui/build/` 目录

## 🛠️ 配置说明

### 环境变量

构建过程中会设置以下环境变量：

```bash
QAX_ENVIRONMENT=true                                    # 启用 Qax 模式
VSCODE_QAX_ENV=prod|dev                                # 环境类型
VSCODE_QAX_CODEGEN_DOMAIN=https://codegen.qianxin-inc.cn  # Qax 域名
VSCODE_QAX_AI_DOMAIN=https://aip.b.qianxin-inc.cn     # AI 域名
VSCODE_QAX_AUTOCOMPLETE=true                           # 启用自动补全
```

### 文本替换

构建过程中会替换以下内容：

- 扩展标识符: `saoudrizwan.claude-dev` → `qi-anxin-group.qax-codegen`
- 视图容器: `claude-dev` → `qax-codegen`
- 域名配置: Cline 域名 → Qax 域名

## 🔍 故障排除

### 常见问题

1. **构建失败**: 检查 Node.js 版本和依赖安装
2. **图标不显示**: 确认 `qax/assets/icons/` 目录下有正确的图标文件
3. **文件未恢复**: 检查备份目录是否存在，手动从备份恢复

### 清理环境

如果需要清理构建环境：

```bash
# 删除备份文件
rm -rf qax/cline-backup

# 删除构建产物
rm -rf dist webview-ui/build

# 删除 VSIX 包
rm -f *.vsix
```

## ⚡ 优化亮点

### 🔧 自动化改进
- **QAX_MODE 自动配置**: 构建时自动确保 QAX_MODE 设置为 true，无需手动操作
- **智能备份策略**: 只保留最近3次备份，自动清理旧备份，节省磁盘空间
- **环境变量统一管理**: 集中管理所有环境变量配置

### 🚀 性能优化
- **复用 Cline 构建流程**: 最大化复用原有构建逻辑，减少重复工作
- **简化文本替换**: 只保留核心必要的替换规则，减少处理时间和误替换风险
- **清理代码结构**: 移除冗余逻辑，提高构建速度

### 🛡️ 可靠性提升
- **错误处理优化**: 改进错误处理机制，提供更清晰的错误信息
- **构建验证**: 自动验证备份完整性，确保文件安全
- **回滚机制**: 构建失败时自动恢复原始文件

## 📝 开发说明

### 修改构建配置

主要配置文件：

- `qax/scripts/qax-config.js`: 构建配置和文本替换规则（已优化）
- `qax/package.json`: Qax 扩展的 package.json 配置
- `qax/scripts/build-qax.js`: 主构建脚本（已优化）
- `qax/scripts/file-processor.js`: 文件处理器（已优化）

### 添加新的资源文件

在 `qax/scripts/qax-config.js` 的 `TEMP_COPY_FILES` 数组中添加：

```javascript
{
    from: 'qax/path/to/source',
    to: 'target/path'
}
```

### 添加文本替换规则

在 `qax/scripts/qax-config.js` 的 `TEXT_REPLACEMENT_PATTERNS` 中添加文件和替换规则。

**注意**: 新的替换规则已经简化，只保留核心必要的替换，避免过度复杂化。
