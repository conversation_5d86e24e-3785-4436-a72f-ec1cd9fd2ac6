/**
 * Qax Codegen 构建配置文件
 * 集中管理 Qax Codegen 扩展的所有配置信息
 * <AUTHOR>
 */

const path = require("path")
const fs = require("fs")

// 获取项目根目录
const ROOT_DIR = path.resolve(__dirname, "../..")
const QAX_DIR = path.resolve(__dirname, ".")

// 环境配置 - 简化版，只保留域名配置
const ENVIRONMENTS = {
	dev: {
		name: "development",
		qaxDomain: "https://codegen-dev.qianxin-inc.cn",
		aiDomain: "https://aip.b.qianxin-inc.cn",
	},
	test: {
		name: "testing",
		qaxDomain: "https://codegen-test.qianxin-inc.cn",
		aiDomain: "https://aip.b.qianxin-inc.cn",
	},
	prod: {
		name: "production",
		qaxDomain: "https://codegen.qianxin-inc.cn",
		aiDomain: "https://aip.b.qianxin-inc.cn",
	},
}

// 获取当前环境配置
function getEnvironmentConfig(env = "prod") {
	return ENVIRONMENTS[env] || ENVIRONMENTS.prod
}

// 文本替换规则 - 简化版，只保留核心必要的替换
const TEXT_REPLACEMENTS = {
	// 扩展标识符替换（核心）
	'"saoudrizwan.claude-dev"': '"qi-anxin-group.qax-codegen"',
	"'saoudrizwan.claude-dev'": "'qi-anxin-group.qax-codegen'",

	// 命令名称替换（核心）
	'registerCommand("cline.': 'registerCommand("qax-codegen.',
	'executeCommand("cline.': 'executeCommand("qax-codegen.',
	'command: "cline.': 'command: "qax-codegen.',
	'"setContext", "cline.': '"setContext", "qax-codegen.',

	// 视图和容器ID替换（核心）
	'"claude-dev.': '"qax-codegen.',
	"claude-dev-ActivityBar": "qax-codegen-ActivityBar",

	// Walkthrough ID 替换（核心）
	"saoudrizwan.claude-dev#ClineWalkthrough": "qi-anxin-group.qax-codegen#QaxCodegenWalkthrough",

	// Output Channel 名称替换（核心）
	'createOutputChannel("Cline")': 'createOutputChannel("Qax Codegen")',
	'name: "Cline"': 'name: "Qax Codegen"',

	// 日志消息替换（核心）
	'"Cline extension activated"': '"Qax Codegen extension activated"',
	'"ClineProvider instantiated"': '"Qax Codegen Provider instantiated"',
}

// 需要临时复制的文件列表
const TEMP_COPY_FILES = [
	{
		from: "scripts/qax/assets/icons/qax-icon.png",
		to: "assets/icons/qax-icon.png",
	},
	{
		from: "scripts/qax/assets/icons/qax-sidebar-icon.png",
		to: "assets/icons/qax-sidebar-icon.png",
	},
	{
		from: "scripts/qax/docs/README.md",
		to: "README.md",
	},
	{
		from: "scripts/qax/docs/CHANGELOG.md",
		to: "CHANGELOG.md",
	},
	// QAX 专用组件替换 - 构建时临时替换，构建后自动恢复
	{
		from: "webview-ui/src/qax/components/settings/QaxApiOptions.tsx",
		to: "webview-ui/src/components/settings/ApiOptions.tsx",
	},
	// 确保codicon文件被正确复制到扩展包中
	// {
	// 	from: "node_modules/@vscode/codicons/dist/codicon.css",
	// 	to: "node_modules/@vscode/codicons/dist/codicon.css",
	// },
	// {
	// 	from: "node_modules/@vscode/codicons/dist/codicon.ttf",
	// 	to: "node_modules/@vscode/codicons/dist/codicon.ttf",
	// },
]

// 需要应用文本替换的文件模式（这些文件会被临时修改，构建后自动恢复）
const TEXT_REPLACEMENT_PATTERNS = {
	// 核心扩展文件 - 打包必需
	//- 包含命令注册和扩展激活日志的文件
	"src/extension.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	//- 包含视图ID的文件
	"src/core/webview/index.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"src/core/webview/WebviewProvider.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"src/hosts/vscode/VscodeWebviewProvider.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	//- 包含命令调用和 Provider 实例化日志的文件
	"src/core/controller/index.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	// - 包含扩展引用的文件
	"src/core/task/ToolExecutor.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	// - 包含扩展标识符的文件
	"src/standalone/vscode-context.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	// - 包含终端名称的文件
	"src/integrations/terminal/TerminalRegistry.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	// - 包含主题扩展引用的文件
	"src/integrations/theme/getTheme.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},

	// 其他可能， 非必需
	//- 开发和工具文件（可选，用于开发测试）
	// "src/dev/commands/tasks.ts": {
	// 	textReplacements: TEXT_REPLACEMENTS,
	// },
	//- 评估系统文件（可选，用于开发测试）
	// "evals/cli/src/utils/vscode.ts": {
	// 	textReplacements: TEXT_REPLACEMENTS,
	// },
}

// 环境变量配置
function getEnvironmentVariables(env = "prod") {
	const envConfig = getEnvironmentConfig(env)

	return {
		// QAX 基础配置
		QAX_ENVIRONMENT: "true",
		QAX_CODEGEN_DOMAIN: envConfig.qaxDomain,
		QAX_AI_DOMAIN: envConfig.aiDomain,
		QAX_AUTOCOMPLETE: "true",

		// 扩展标识符
		VSCODE_EXTENSION_ID: "qi-anxin-group.qax-codegen",
	}
}

/**
 * 生成构建脚本配置 - 简化版
 * 所有环境都使用相同的打包流程，通过环境变量区分配置
 * @returns {Object} 构建脚本配置
 */
function getBuildScripts() {
	return {
		// 预构建脚本 - 不需要预构建，直接打包
		prebuild: [],

		// 构建脚本 - 所有环境都使用完整的 package 命令进行打包
		build: ["npm run package"],

		// 后构建脚本 - 暂时为空
		postbuild: [],
	}
}

// 默认的 BUILD_SCRIPTS
const BUILD_SCRIPTS = getBuildScripts()

// 构建配置 - 简化版
const BUILD_CONFIG = {
	buildTimeout: 300000, // 5分钟
	maxBackupCount: 3,
	buildSteps: {
		package: "完整打包：类型检查 + 前端构建 + 代码检查 + 主构建 + VSIX 生成（一体化流程）",
	},
}

/**
 * 合并 package.json 配置 - 简化版
 * @param {string} originalPackageJsonPath - 原始 package.json 路径
 * @param {string} qaxPackageJsonPath - Qax package.json 路径
 * @returns {Object} 合并后的 package.json 对象
 */
function mergePackageJson(originalPackageJsonPath, qaxPackageJsonPath) {
	const originalPackage = JSON.parse(fs.readFileSync(originalPackageJsonPath, "utf8"))
	const qaxPackage = JSON.parse(fs.readFileSync(qaxPackageJsonPath, "utf8"))

	// Qax 配置覆盖原始配置
	const mergedPackage = { ...originalPackage, ...qaxPackage }

	console.log(`✅ 已合并 package.json 配置`)
	console.log(`   - 扩展标识符: ${mergedPackage.name}`)
	console.log(`   - 显示名称: ${mergedPackage.displayName}`)
	console.log(`   - 版本号: ${mergedPackage.version}`)

	return mergedPackage
}

// 路径配置
const PATHS = {
	root: ROOT_DIR,
	qax: QAX_DIR,
	originalPackageJson: path.join(ROOT_DIR, "package.json"),
	qaxPackageJson: path.join(QAX_DIR, "package.json"),
	dist: path.join(ROOT_DIR, "dist"),
	webviewBuild: path.join(ROOT_DIR, "webview-ui/build"),
	clineBackup: path.join(ROOT_DIR, "qax/cline-backup"),
}

// 导出配置
module.exports = {
	ROOT_DIR,
	QAX_DIR,
	ENVIRONMENTS,
	TEXT_REPLACEMENTS,
	TEMP_COPY_FILES,
	TEXT_REPLACEMENT_PATTERNS,
	BUILD_SCRIPTS,
	BUILD_CONFIG,
	PATHS,
	getEnvironmentConfig,
	getEnvironmentVariables,
	getBuildScripts,
	mergePackageJson,
}
