#!/usr/bin/env node

/**
 * QAX扩展打包脚本
 */

const fs = require("fs")
const path = require("path")
const { execSync } = require("child_process")
const FileProcessor = require("./qax/file-processor")
const {
	ROOT_DIR,
	QAX_DIR,
	getBuildScripts,
	BUILD_CONFIG,
	getEnvironmentConfig,
	getEnvironmentVariables,
	PATHS,
} = require("./qax/qax-config")

class QaxBuilder {
	constructor(options = {}) {
		this.env = options.env || "prod"
		this.cleanMode = options.clean || false

		this.fileProcessor = new FileProcessor()
		this.envConfig = getEnvironmentConfig(this.env)
		this.envVars = getEnvironmentVariables(this.env)
		this.buildScripts = getBuildScripts()

		console.log("🚀 QAX扩展构建器初始化")
		console.log(`   📋 环境: ${this.env}`)
	}

	async build() {
		console.log("\n🚀 开始打包QAX扩展...")

		try {
			// 1. 复制QAX资源（包括替换 ApiOptions 组件）
			this.fileProcessor.copyQaxAssets()

			// 2. 生成合并的package.json
			const mergedPackage = this.fileProcessor.generateMergedPackageJson()

			// 3. 应用文本替换
			this.fileProcessor.applyTextReplacements()

			// 4. 设置环境变量
			this.setEnvironmentVariables()

			// 5. 执行打包
			this.runBuild()

			// 6. 生成VSIX包
			const vsixPath = this.generateVsixPackage(mergedPackage)

			// 7. 恢复package.json
			this.fileProcessor.restorePackageJson()

			console.log("\n✅ QAX扩展打包完成!")
			console.log(`📦 VSIX包: ${vsixPath}`)
		} catch (error) {
			console.error("\n❌ 打包失败:", error.message)
			throw error
		} finally {
			// 清理临时文件
			this.fileProcessor.cleanup()
		}
	}

	setEnvironmentVariables() {
		console.log("🌐 设置环境变量...")
		Object.entries(this.envVars).forEach(([key, value]) => {
			process.env[key] = value
			console.log(`   ✅ ${key}=${value}`)
		})
	}

	runBuild() {
		console.log("🔨 执行打包脚本...")

		this.buildScripts.build.forEach((script) => {
			console.log(`   🔄 ${script}`)
			execSync(script, {
				cwd: ROOT_DIR,
				stdio: "inherit",
				env: { ...process.env, ...this.envVars },
			})
		})

		console.log("✅ 打包脚本执行完成")
	}

	generateVsixPackage(packageJson) {
		console.log("📦 生成VSIX包...")

		const vsixName = `${packageJson.name}-v${packageJson.version}.vsix`
		const vsixPath = path.join(ROOT_DIR, vsixName)

		// 删除旧的VSIX包
		if (fs.existsSync(vsixPath)) {
			fs.unlinkSync(vsixPath)
		}

		// 生成新的VSIX包
		execSync(`npx vsce package --out ${vsixName}`, {
			cwd: ROOT_DIR,
			stdio: "inherit",
		})

		return vsixPath
	}

	async clean() {
		console.log("🧹 开始清理构建环境...")

		try {
			// 清理构建产物
			const pathsToClean = [PATHS.dist, PATHS.webviewBuild]

			pathsToClean.forEach((cleanPath) => {
				if (fs.existsSync(cleanPath)) {
					fs.rmSync(cleanPath, { recursive: true, force: true })
					console.log(`   🗑️  删除: ${path.relative(ROOT_DIR, cleanPath)}`)
				}
			})

			// 清理VSIX文件
			const vsixFiles = fs.readdirSync(ROOT_DIR).filter((file) => file.endsWith(".vsix"))
			vsixFiles.forEach((vsixFile) => {
				const vsixPath = path.join(ROOT_DIR, vsixFile)
				fs.unlinkSync(vsixPath)
				console.log(`   🗑️  删除VSIX: ${vsixFile}`)
			})

			// 清理临时文件
			this.fileProcessor.cleanup()

			console.log("✅ 清理完成!")
		} catch (error) {
			console.error("❌ 清理失败:", error.message)
			throw error
		}
	}
}

// 主程序入口
async function main() {
	const args = process.argv.slice(2)
	const options = {}

	// 解析命令行参数
	args.forEach((arg) => {
		if (arg.startsWith("--env=")) {
			options.env = arg.split("=")[1]
		} else if (arg === "--clean") {
			options.clean = true
		}
	})

	const builder = new QaxBuilder(options)

	try {
		if (options.clean) {
			await builder.clean()
		} else {
			await builder.build()
		}
	} catch (error) {
		console.error("构建失败:", error)
		process.exit(1)
	}
}

// 运行主程序
if (require.main === module) {
	main()
}
