syntax = "proto3";

package qax;
import "cline/common.proto";
option java_package = "bot.qax.proto";
option java_multiple_files = true;

// Service for account-related operations
service QaxAccountService {
  rpc qaxLoginClicked(cline.EmptyRequest) returns (cline.String);
  rpc qaxLogoutClicked(cline.EmptyRequest) returns (cline.Empty);
  rpc subscribeToQaxAuthStatusUpdate(cline.EmptyRequest) returns (stream QaxAuthState);
  rpc qaxAuthStateChanged(QaxAuthStateChangedRequest) returns (QaxAuthState);
}

// QAX 相关消息类型
message QaxAuthStateChangedRequest {
  cline.Metadata metadata = 1;
  QaxUserInfo user = 2;
}

message QaxAuthState { optional QaxUserInfo user = 1; }

// Qax User's information based on JWT token structure
message QaxUserInfo {
  string sub = 1;                      // Subject (user ID)
  optional string display_name = 2;    // Display name from JWT
  optional string name = 3;            // Username from JWT
  optional string email = 4;           // Email from JWT
  optional string employee_number = 5; // Employee number from JWT
  optional string iss = 6;             // Issuer from JWT
  repeated string aud = 7;             // Audience from JWT
  int64 exp = 8;                       // Expiration time from JWT
  int64 iat = 9;                       // Issued at time from JWT
}
