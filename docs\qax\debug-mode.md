# QAX Codegen 调试模式配置

## 概述

在开发和调试 QAX Codegen 扩展时，您可能需要使用 Cline 的回调地址来进行认证测试。本文档说明如何配置调试模式。

## 回调地址配置

### 默认行为
- **开发模式**（默认）：使用 `vscode://saoudrizwan.claude-dev/qax-codegen-auth`
- **打包模式**：使用 `vscode://qi-anxin-group.qax-codegen/qax-codegen-auth`

### 工作原理

系统通过 `VSCODE_EXTENSION_ID` 环境变量来决定使用哪个扩展 ID：

- **未设置环境变量**（开发时）：默认使用 `saoudrizwan.claude-dev`
- **设置环境变量**（打包时）：使用 `qi-anxin-group.qax-codegen`

### 手动配置

如果需要手动指定扩展 ID：

```bash
# 使用 QAX 扩展 ID
export VSCODE_EXTENSION_ID=qi-anxin-group.qax-codegen

# 使用 Cline 扩展 ID（默认）
export VSCODE_EXTENSION_ID=saoudrizwan.claude-dev
```

### 使用示例

#### 开发环境（默认）
直接启动 VSCode 调试，无需额外配置：

```bash
# 启动 VSCode 调试
code --extensionDevelopmentPath=/path/to/your/extension
```

#### 测试打包行为
如果需要在开发环境中测试打包后的行为：

```bash
# 设置扩展 ID 为 QAX
export VSCODE_EXTENSION_ID=qi-anxin-group.qax-codegen

# 启动 VSCode 调试
code --extensionDevelopmentPath=/path/to/your/extension
```

### 验证配置

启动扩展后，在控制台中查看日志：

```
# 开发模式（默认）
[QAX Auth] Callback URL: vscode://saoudrizwan.claude-dev/qax-codegen-auth

# 打包模式
[QAX Auth] Callback URL: vscode://qi-anxin-group.qax-codegen/qax-codegen-auth
```

## 构建流程

### 开发时
- 不设置 `VSCODE_EXTENSION_ID` 环境变量
- 系统自动使用 `saoudrizwan.claude-dev` 作为扩展 ID
- 回调地址为 `vscode://saoudrizwan.claude-dev/qax-codegen-auth`

### 打包时
- 构建脚本自动设置 `VSCODE_EXTENSION_ID=qi-anxin-group.qax-codegen`
- 系统使用 QAX 的扩展 ID
- 回调地址为 `vscode://qi-anxin-group.qax-codegen/qax-codegen-auth`

## 注意事项

1. **QAX Codegen 服务器配置**：确保 QAX Codegen 服务器的回调白名单中包含两个回调地址
2. **URI 处理器**：确保 VSCode 能够正确处理 `/qax-codegen-auth` 路径
3. **环境变量优先级**：手动设置的 `VSCODE_EXTENSION_ID` 会覆盖默认行为

## 故障排除

### 回调地址不正确
- 检查 `VSCODE_EXTENSION_ID` 环境变量设置
- 查看控制台日志确认使用的回调地址
- 重启 VSCode 确保环境变量生效

### 认证失败
- 确认 QAX Codegen 服务器支持当前使用的回调地址
- 检查网络连接和防火墙设置
- 查看浏览器开发者工具的网络请求

## 相关文件

- `src/shared/qax.ts` - 回调 URL 生成函数
- `src/qax/services/auth/QaxAuthService.ts` - 认证服务实现
- `src/extension.ts` - URI 回调处理器
