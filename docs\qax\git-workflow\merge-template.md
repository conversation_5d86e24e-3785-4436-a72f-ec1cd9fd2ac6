# webview-ui/src/App.tsx 合并计划

## 🎯 核心策略
**基准**: codegen (v3.20.13) 为绝对基准
**目标**: 在新架构基础上重新实现 QAX 功能

## 📊 变更分析

### 基准版本核心变更 (一句话概括)
- **主要变更**: 引入 gRPC 客户端架构，使用 UiServiceClient 替代直接 WebviewMessage，新增 useClineAuth hook
- **影响评估**: 中等 - QAX 组件选择逻辑需要适配新的认证和通信架构

### QAX 功能核心要点 (< 5 个关键点)
```typescript
// 1. QAX 模式配置
import { QAX_MODE } from "./qax/utils/config"

// 2. QAX 组件导入
import QaxAccountView from "./qax/components/account/QaxAccountView"
import QaxWelcomeView from "./qax/components/welcome/WelcomeView"
import QaxSettingsView from "./qax/components/settings/QaxSettingsView"

// 3. 条件组件选择
const AccountViewComponent = QAX_MODE ? QaxAccountView : AccountView
const WelcomeViewComponent = QAX_MODE ? QaxWelcomeView : WelcomeView
const SettingsViewComponent = QAX_MODE ? QaxSettingsView : SettingsView

// 4. 渲染逻辑中使用条件组件
{showWelcome ? <WelcomeViewComponent /> : /* 其他内容 */}
{showSettings && <SettingsViewComponent onDone={hideSettings} />}
{showAccount && <AccountViewComponent onDone={hideAccount} />}
```

## 🚀 实施计划

### 重置基准
```bash
git restore --source=v3.20.13 -- webview-ui/src/App.tsx
```

### 集成步骤 (按优先级排序)
1. **[必须]** 添加 QAX 组件导入和配置
2. **[必须]** 实现条件组件选择逻辑
3. **[必须]** 在渲染逻辑中使用条件组件
4. **[重要]** 确保 QAX 组件与新的 gRPC 架构兼容
5. **[可选]** 优化组件加载性能

### 风险控制
- **🔴 高风险**: QAX 组件可能依赖旧的 WebviewMessage 架构
- **🟡 中风险**: 条件渲染逻辑可能影响组件状态管理

## ✅ 验证清单
- [ ] 编译通过
- [ ] QAX 模式下显示正确的组件
- [ ] Cline 模式下功能正常
- [ ] 组件切换无状态丢失
