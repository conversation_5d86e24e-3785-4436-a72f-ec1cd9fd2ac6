/**
 * Qax 大模型平台 API Handler
 * <AUTHOR>
 */

import { Anthropic } from "@anthropic-ai/sdk"
import { ModelInfo, OpenAiCompatibleModelInfo, openAiModelInfoSaneDefaults } from "@shared/api"
import { getQaxAIPlatformBaseUrl } from "@shared/qax"
import OpenAI from "openai"
import type { ChatCompletionReasoningEffort } from "openai/resources/chat/completions"
import { ApiHandler } from "../../../core/api"
import { withRetry } from "../../../core/api/retry"
import { convertToOpenAiMessages } from "../../../core/api/transform/openai-format"
import { convertToR1Format } from "../../../core/api/transform/r1-format"
import { ApiStream } from "../../../core/api/transform/stream"

interface QaxHandlerOptions {
	qaxApiKey?: string
	qaxApiBaseUrl?: string
	qaxModelId?: string
	qaxModelInfo?: OpenAiCompatibleModelInfo
	qaxHeaders?: Record<string, string>
	reasoningEffort?: string
}

export class QaxHandler implements ApiHandler {
	private options: QaxHandlerOptions
	private client: OpenAI | undefined

	constructor(options: QaxHandlerOptions) {
		this.options = options
	}

	private ensureClient(): OpenAI {
		if (!this.client) {
			if (!this.options.qaxApiKey) {
				throw new Error("QAX API key is required")
			}
			const baseURL = this.options.qaxApiBaseUrl || getQaxAIPlatformBaseUrl()

			try {
				this.client = new OpenAI({
					baseURL,
					apiKey: this.options.qaxApiKey,
					defaultHeaders: this.options.qaxHeaders,
				})
			} catch (error: any) {
				throw new Error(`Error creating QAX client: ${error.message}`)
			}
		}
		return this.client
	}

	@withRetry()
	async *createMessage(systemPrompt: string, messages: Anthropic.Messages.MessageParam[]): ApiStream {
		try {
			// Validate request before proceeding (following Cline 3.18.12 pattern)
			//await this.qaxAccountService.validateRequest()

			const client = this.ensureClient()
			const modelId = this.options.qaxModelId ?? ""

			const isDeepseekReasoner = modelId.includes("deepseek-reasoner")
			const isR1FormatRequired = this.options.qaxModelInfo?.isR1FormatRequired ?? false
			const isReasoningModelFamily = modelId.includes("o1") || modelId.includes("o3") || modelId.includes("o4")

			let openAiMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [
				{ role: "system", content: systemPrompt },
				...convertToOpenAiMessages(messages),
			]
			let temperature: number | undefined =
				this.options.qaxModelInfo?.temperature ?? openAiModelInfoSaneDefaults.temperature
			// 如果 temperature 是 0，则设置为 0.6
			if (temperature === 0) {
				temperature = 0.6
			}
			let reasoningEffort: ChatCompletionReasoningEffort | undefined
			let maxTokens: number | undefined

			if (this.options.qaxModelInfo?.maxTokens && this.options.qaxModelInfo.maxTokens > 0) {
				maxTokens = Number(this.options.qaxModelInfo.maxTokens)
			} else {
				maxTokens = undefined
			}

			if (isDeepseekReasoner || isR1FormatRequired) {
				openAiMessages = convertToR1Format([{ role: "user", content: systemPrompt }, ...messages])
			}

			if (isReasoningModelFamily) {
				openAiMessages = [{ role: "developer", content: systemPrompt }, ...convertToOpenAiMessages(messages)]
				temperature = undefined // does not support temperature
				reasoningEffort = (this.options.reasoningEffort as ChatCompletionReasoningEffort) || "medium"
			}

			const requestPayload = {
				model: modelId,
				messages: openAiMessages,
				temperature,
				max_tokens: maxTokens,
				reasoning_effort: reasoningEffort,
				stream: true,
				stream_options: { include_usage: true },
			}

			const stream = (await client.chat.completions.create(requestPayload)) as any

			for await (const chunk of stream) {
				// Check for stream error
				if ("error" in chunk) {
					const error = chunk.error
					console.error(`QAX API Error: ${error?.code} - ${error?.message}`)
					throw new Error(`QAX API Error ${error.code}: ${error.message}`)
				}

				// Check for mid-stream error via finish_reason
				const choice = chunk.choices?.[0]
				if ((choice?.finish_reason as string) === "error") {
					const choiceWithError = choice as any
					if (choiceWithError.error) {
						const error = choiceWithError.error
						console.error(`QAX Mid-Stream Error: ${error.code || error.type || "Unknown"} - ${error.message}`)
						throw new Error(`QAX Mid-Stream Error: ${error.code || error.type || "Unknown"} - ${error.message}`)
					} else {
						throw new Error("QAX Mid-Stream Error: Stream terminated with error status but no error details provided")
					}
				}

				const delta = choice?.delta
				if (delta?.content) {
					yield {
						type: "text",
						text: delta.content,
					}
				}

				if (delta && "reasoning_content" in delta && delta.reasoning_content) {
					yield {
						type: "reasoning",
						reasoning: (delta.reasoning_content as string | undefined) || "",
					}
				}

				if (chunk.usage) {
					yield {
						type: "usage",
						inputTokens: chunk.usage.prompt_tokens || 0,
						outputTokens: chunk.usage.completion_tokens || 0,
						// @ts-ignore-next-line
						cacheReadTokens: chunk.usage.prompt_tokens_details?.cached_tokens || 0,
						// @ts-ignore-next-line
						cacheWriteTokens: chunk.usage.prompt_cache_miss_tokens || 0,
					}
				}
			}
		} catch (error) {
			// Enhanced error handling following Cline 3.18.12 pattern
			if (error.code === "ERR_BAD_REQUEST" || error.status === 401) {
				throw new Error("Unauthorized: Please check your QAX API key and try again.")
			} else if (error.code === "insufficient_credits" || error.status === 402) {
				throw new Error(error.error ? JSON.stringify(error.error) : "Insufficient credits or unknown error.")
			}
			console.error("QAX API Error:", error)
			throw error instanceof Error ? error : new Error(`${error}`)
		}
	}

	getModel(): { id: string; info: ModelInfo } {
		return {
			id: this.options.qaxModelId ?? "",
			info: this.options.qaxModelInfo ?? openAiModelInfoSaneDefaults,
		}
	}
}
