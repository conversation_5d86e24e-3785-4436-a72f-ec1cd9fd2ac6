# QAX Todo 功能架构分析

## Cline 工具系统标准架构

### 工具定义标准格式

基于对 Cline v3.20.13 的实际代码分析，标准工具遵循以下模式：

```typescript
// 标准工具结构示例
export const toolName = "ToolName"  // PascalCase

export const toolDefinition: ToolDefinition = {
    name: toolName,
    descriptionForAgent: "...",
    inputSchema: {
        type: "object",
        properties: {
            parameter_name: {  // snake_case
                type: "string",
                description: "..."
            }
        },
        required: ["parameter_name"]
    }
}
```

### 实际工具命名规范证据

通过代码分析发现，Cline 工具名称统一使用 **PascalCase**：

- `"Bash"` (bashTool)
- `"Read"` (readTool)
- `"Write"` (writeTool)
- `"LS"` (lsTool)
- `"Grep"` (grepTool)
- `"AskQuestion"` (askQuestionTool)
- `"AttemptCompletion"` (attemptCompletionTool)
- `"BrowserAction"` (browserActionTool)
- `"MultiEdit"` (editTool)
- `"ListCodeDefinitionNames"` (listCodeDefinitionNamesTool)

### 文件组织模式

**标准模式**：
- 每个工具一个文件
- 直接在 `src/core/tools/` 目录下
- 无复杂的子目录结构
- 无分离的核心逻辑、状态管理等模块

**示例**：
```
src/core/tools/
├── bashTool.ts
├── readTool.ts
├── writeTool.ts
├── lsTool.ts
└── grepTool.ts
```

## QAX Todo 当前实现分析

### 当前架构问题

#### 1. 工具命名不符合规范
```typescript
// 当前（错误）
export const qaxUpdateTodoListToolName = "qax_update_todo_list"  // snake_case

// 应为（正确）
export const qaxTodoListToolName = "QaxTodoList"  // PascalCase
```

#### 2. 文件位置不合理
```
当前位置: src/core/tools/QaxTodoListTool.ts
建议位置: src/qax/tools/QaxTodoListTool.ts
```

理由：
- QAX 特定功能应与 Cline 核心工具分离
- `src/qax/` 目录已存在，用于 QAX 功能
- 避免与 Cline 标准工具混合

#### 3. UI 处理逻辑过于复杂
ChatRow.tsx 中的处理逻辑（709-800 行）：
- 复杂的统计计算
- 消息历史遍历
- 状态比较逻辑
- 不符合其他工具的简单显示模式

#### 4. 消息类型命名冗长
```typescript
// 当前
| "qax_user_edit_todos"  // 过于冗长

// 建议
| "qax_todo_update"      // 简洁明了
```

### 工具执行流程分析

#### 标准流程
1. **定义阶段**：在工具文件中定义 ToolDefinition
2. **注册阶段**：在 model prompts 中注册到工具列表
3. **执行阶段**：在 ToolExecutor.ts 中处理工具调用
4. **UI 展示**：在 ChatRow.tsx 中渲染工具结果

#### QAX Todo 当前流程问题
- ✅ 定义阶段：正确
- ❌ 注册阶段：claude4.ts 中缺失
- ✅ 执行阶段：正确，但工具名称不一致
- ❌ UI 展示：过于复杂

## 重构目标架构

### 新的文件组织
```
src/qax/tools/
└── QaxTodoListTool.ts          # 迁移并重构

src/core/controller/qaxTodo/    # 保持不变
├── addQaxTodoItem.ts
├── deleteQaxTodoItem.ts
├── getQaxTodoList.ts
├── updateQaxTodoItemStatus.ts
└── updateQaxTodoList.ts

webview-ui/src/qax/components/todo/  # 保持不变
└── QaxTodoList.tsx
```

### 新的工具定义
```typescript
// src/qax/tools/QaxTodoListTool.ts
export const qaxTodoListToolName = "QaxTodoList"

export const qaxTodoListToolDefinition: ToolDefinition = {
    name: qaxTodoListToolName,
    descriptionForAgent: `Replace the entire TODO list with an updated checklist...`,
    inputSchema: {
        type: "object",
        properties: {
            todos: {
                type: "string",
                description: "The complete todo list as a markdown checklist..."
            }
        },
        required: ["todos"]
    }
}
```

### 简化的 UI 处理
```typescript
// webview-ui/src/components/chat/ChatRow.tsx
case "QaxTodoList":
    return (
        <>
            <div style={headerStyle}>
                {toolIcon("checklist", "green")}
                <span style={{ fontWeight: "bold" }}>
                    {message.type === "ask" ? "Cline wants to update todo list:" : "Todo List Updated"}
                </span>
            </div>
            {/* 简化的显示逻辑 */}
        </>
    )
```

## 兼容性考虑

### 向后兼容策略
1. **渐进式重构**：分阶段执行，确保每个阶段都能正常工作
2. **保留备份**：重构过程中保留原文件作为备份
3. **消息历史兼容**：考虑现有消息历史中的工具名称

### 数据迁移
- Todo 数据存储在 TaskState 中，不受工具名称变更影响
- gRPC 服务接口保持不变
- UI 组件功能保持完整

## 性能影响分析

### 正面影响
- 简化 UI 逻辑，减少不必要的计算
- 标准化工具处理，提高一致性
- 减少代码复杂度，提高维护性

### 潜在风险
- 工具名称变更可能需要清理现有消息历史
- 需要确保所有引用都正确更新

### 缓解措施
- 充分测试所有功能点
- 分阶段部署，及时发现问题
- 保留回滚机制
