#!/usr/bin/env node

/**
 * 综合的上下文提取测试程序
 * 全面测试inter-declaration检测逻辑的各种边界情况
 */

const fs = require("fs")
const path = require("path")

// 扩展的测试文件内容
const testFiles = {
	"complex.js": `// File header
import { utils } from './utils';

/**
 * First function with complex structure
 */
function firstFunction(param1, param2) {
    const result = param1 + param2;
    if (result > 0) {
        return result * 2;
    }
    return 0;
}

// Multi-line comment between functions
// This is where we test inter-declaration detection
// Another comment line

/**
 * Second function with nested structures
 */
function secondFunction() {
    const nested = {
        method: function() {
            return 'nested';
        }
    };
    return nested;
}

// Comment before class

class MyClass {
    constructor() {
        this.value = 0;
    }
    
    method() {
        return this.value;
    }
}

// End of file comment`,

	"complex.ts": `interface BaseInterface {
    id: number;
    name: string;
}

// Interface separator comment
// Multiple lines here

interface ExtendedInterface extends BaseInterface {
    extra: boolean;
}

// Class separator comment

class ServiceClass implements ExtendedInterface {
    id: number = 0;
    name: string = '';
    extra: boolean = false;
    
    constructor(data: Partial<ExtendedInterface>) {
        Object.assign(this, data);
    }
    
    process(): void {
        console.log('processing');
    }
}

// Function separator comment

export function utilityFunction<T>(data: T): T {
    return data;
}

// Another function

export async function asyncFunction(): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 100));
}`,

	"complex.py": `"""Module docstring
This is a complex Python file for testing
"""
import os
import sys

# Global variable
GLOBAL_CONSTANT = 42

def first_function(param1, param2):
    """First function docstring"""
    result = param1 + param2
    if result > 0:
        return result * 2
    return 0

# Comment between functions
# Multiple comment lines
# Testing inter-declaration detection

def second_function():
    """Second function with nested logic"""
    def nested_function():
        return "nested"
    
    return nested_function()

# Comment before class

class MyClass:
    """Class docstring"""
    
    def __init__(self, value=0):
        self.value = value
    
    def method(self):
        """Method docstring"""
        return self.value * 2

# Another comment block

class ChildClass(MyClass):
    """Child class docstring"""
    
    def __init__(self, value=0, extra=None):
        super().__init__(value)
        self.extra = extra
    
    def extended_method(self):
        return self.method() + (self.extra or 0)

# End of file`,
}

// 扩展的测试用例
const comprehensiveTestCases = [
	// JavaScript测试
	{
		name: "JS - 光标在第一个函数内部",
		fileName: "complex.js",
		cursorLine: 8, // "const result = param1 + param2;"
		cursorChar: 15,
		expectedStrategy: "meaningful-parent",
		description: "光标在函数体内，应该使用meaningful-parent策略",
	},
	{
		name: "JS - 光标在函数之间的多行注释",
		fileName: "complex.js",
		cursorLine: 15, // "// This is where we test inter-declaration detection"
		cursorChar: 10,
		expectedStrategy: "inter-declaration",
		description: "光标在两个函数之间的注释中，应该使用inter-declaration策略",
	},
	{
		name: "JS - 光标在函数和类之间",
		fileName: "complex.js",
		cursorLine: 30, // "// Comment before class"
		cursorChar: 5,
		expectedStrategy: "inter-declaration",
		description: "光标在函数和类之间，应该使用inter-declaration策略",
	},
	{
		name: "JS - 光标在类方法内部",
		fileName: "complex.js",
		cursorLine: 37, // "return this.value;"
		cursorChar: 12,
		expectedStrategy: "meaningful-parent",
		description: "光标在类方法内部，应该使用meaningful-parent策略",
	},
	{
		name: "JS - 光标在文件末尾",
		fileName: "complex.js",
		cursorLine: 41, // "// End of file comment"
		cursorChar: 10,
		expectedStrategy: "inter-declaration",
		description: "光标在文件末尾，应该使用inter-declaration策略",
	},

	// TypeScript测试
	{
		name: "TS - 光标在接口内部",
		fileName: "complex.ts",
		cursorLine: 2, // "name: string;"
		cursorChar: 8,
		expectedStrategy: "meaningful-parent",
		description: "光标在接口定义内部，应该使用meaningful-parent策略",
	},
	{
		name: "TS - 光标在两个接口之间",
		fileName: "complex.ts",
		cursorLine: 6, // "// Interface separator comment"
		cursorChar: 15,
		expectedStrategy: "inter-declaration",
		description: "光标在两个接口之间，应该使用inter-declaration策略",
	},
	{
		name: "TS - 光标在类构造函数内部",
		fileName: "complex.ts",
		cursorLine: 19, // "Object.assign(this, data);"
		cursorChar: 20,
		expectedStrategy: "meaningful-parent",
		description: "光标在类构造函数内部，应该使用meaningful-parent策略",
	},
	{
		name: "TS - 光标在类和函数之间",
		fileName: "complex.ts",
		cursorLine: 27, // "// Function separator comment"
		cursorChar: 8,
		expectedStrategy: "inter-declaration",
		description: "光标在类和函数之间，应该使用inter-declaration策略",
	},
	{
		name: "TS - 光标在泛型函数内部",
		fileName: "complex.ts",
		cursorLine: 31, // "    return data;"
		cursorChar: 8,
		expectedStrategy: "meaningful-parent",
		description: "光标在泛型函数内部，应该使用meaningful-parent策略",
	},

	// Python测试
	{
		name: "PY - 光标在函数内部",
		fileName: "complex.py",
		cursorLine: 11, // "result = param1 + param2"
		cursorChar: 10,
		expectedStrategy: "meaningful-parent",
		description: "光标在Python函数内部，应该使用meaningful-parent策略",
	},
	{
		name: "PY - 光标在函数之间的注释",
		fileName: "complex.py",
		cursorLine: 17, // "# Multiple comment lines"
		cursorChar: 5,
		expectedStrategy: "inter-declaration",
		description: "光标在Python函数之间的注释中，应该使用inter-declaration策略",
	},
	{
		name: "PY - 光标在类方法内部",
		fileName: "complex.py",
		cursorLine: 35, // "return self.value * 2"
		cursorChar: 15,
		expectedStrategy: "meaningful-parent",
		description: "光标在Python类方法内部，应该使用meaningful-parent策略",
	},
	{
		name: "PY - 光标在两个类之间",
		fileName: "complex.py",
		cursorLine: 38, // "# Another comment block"
		cursorChar: 8,
		expectedStrategy: "inter-declaration",
		description: "光标在两个Python类之间，应该使用inter-declaration策略",
	},
	{
		name: "PY - 光标在文件开头的模块文档字符串后",
		fileName: "complex.py",
		cursorLine: 4, // "import os"
		cursorChar: 5,
		expectedStrategy: "inter-declaration",
		description: "光标在模块导入部分，应该使用inter-declaration策略",
	},
]

console.log("🧪 开始综合上下文提取测试")
console.log("=".repeat(80))
console.log(`📊 总共 ${comprehensiveTestCases.length} 个测试用例`)
console.log("")

let passedTests = 0
const totalTests = comprehensiveTestCases.length

// 运行所有测试
for (let i = 0; i < comprehensiveTestCases.length; i++) {
	const testCase = comprehensiveTestCases[i]
	console.log(`📋 测试 ${i + 1}/${totalTests}: ${testCase.name}`)
	console.log(`📝 描述: ${testCase.description}`)
	console.log(`📍 光标位置: 第${testCase.cursorLine + 1}行, 第${testCase.cursorChar + 1}字符`)

	try {
		const content = testFiles[testCase.fileName]
		const lines = content.split("\n")

		// 显示实际的光标行内容用于调试
		const actualLine = lines[testCase.cursorLine] || ""
		console.log(`📄 实际行内容: "${actualLine}"`)

		const cursorOffset = lines.slice(0, testCase.cursorLine).join("\n").length + testCase.cursorChar

		// 使用改进的声明检测
		const declarations = findTopLevelDeclarationsImproved(content)

		// 检查光标是否在声明内部
		const insideDeclaration = declarations.find((decl) => {
			return cursorOffset > decl.bodyStart && cursorOffset < decl.end
		})

		let strategy
		let debugInfo = ""

		if (insideDeclaration) {
			strategy = "meaningful-parent"
			debugInfo = `光标在${insideDeclaration.type}内部 (${insideDeclaration.start}-${insideDeclaration.end})`
		} else {
			// 检查是否在声明之间
			let precedingDeclaration = null
			let followingDeclaration = null

			for (const decl of declarations) {
				if (decl.end <= cursorOffset) {
					precedingDeclaration = decl
				} else if (decl.start > cursorOffset && !followingDeclaration) {
					followingDeclaration = decl
					break
				}
			}

			if (precedingDeclaration || followingDeclaration) {
				strategy = "inter-declaration"
				const prevInfo = precedingDeclaration
					? `${precedingDeclaration.type}(${precedingDeclaration.start}-${precedingDeclaration.end})`
					: "none"
				const nextInfo = followingDeclaration
					? `${followingDeclaration.type}(${followingDeclaration.start}-${followingDeclaration.end})`
					: "none"
				debugInfo = `光标在声明之间: 前=${prevInfo}, 后=${nextInfo}`
			} else {
				strategy = "context-window"
				debugInfo = "未找到相邻声明"
			}
		}

		console.log(`✅ 检测到策略: ${strategy}`)
		console.log(`📊 找到 ${declarations.length} 个顶级声明`)
		console.log(`🔍 调试信息: ${debugInfo}`)

		// 检查策略是否匹配预期
		const strategyMatches = strategy === testCase.expectedStrategy
		if (strategyMatches) {
			console.log(`✅ 策略匹配预期: ${testCase.expectedStrategy}`)
			passedTests++
		} else {
			console.log(`❌ 策略不匹配! 预期: ${testCase.expectedStrategy}, 实际: ${strategy}`)

			// 详细调试信息
			console.log(`🔍 详细调试:`)
			console.log(`   光标偏移: ${cursorOffset}`)
			console.log(`   声明列表:`)
			declarations.forEach((decl, idx) => {
				const isInside = cursorOffset > decl.bodyStart && cursorOffset < decl.end
				const isBefore = decl.start > cursorOffset
				const isAfter = decl.end <= cursorOffset
				console.log(
					`     ${idx + 1}. ${decl.type} (${decl.start}-${decl.end}, body:${decl.bodyStart}) ${isInside ? "[INSIDE]" : ""} ${isBefore ? "[BEFORE]" : ""} ${isAfter ? "[AFTER]" : ""}`,
				)
			})
		}
	} catch (error) {
		console.log(`❌ 测试失败，错误: ${error.message}`)
		console.log(`错误堆栈: ${error.stack}`)
	}

	console.log("")
}

console.log("🏁 最终测试总结")
console.log("=".repeat(80))
console.log(`✅ 通过: ${passedTests}/${totalTests}`)
console.log(`❌ 失败: ${totalTests - passedTests}/${totalTests}`)
console.log(`📊 成功率: ${Math.round((passedTests / totalTests) * 100)}%`)

if (passedTests === totalTests) {
	console.log("🎉 所有综合测试都通过了!")
	console.log("✨ inter-declaration检测逻辑工作正常!")
	process.exit(0)
} else {
	console.log("⚠️  部分测试失败，需要进一步调整。")
	process.exit(1)
}

// 改进的顶级声明查找函数
function findTopLevelDeclarationsImproved(content) {
	const declarations = []

	// JavaScript/TypeScript函数 (包括export function)
	const functionRegex = /(export\s+)?function\s+\w+[^{]*\{/g
	let match
	while ((match = functionRegex.exec(content)) !== null) {
		const start = match.index
		const bodyStart = start + match[0].length
		const end = findMatchingBrace(content, start)
		declarations.push({
			type: "function",
			start,
			bodyStart,
			end,
		})
	}

	// JavaScript/TypeScript类
	const classRegex = /class\s+\w+[^{]*\{/g
	while ((match = classRegex.exec(content)) !== null) {
		const start = match.index
		const bodyStart = start + match[0].length
		const end = findMatchingBrace(content, start)
		declarations.push({
			type: "class",
			start,
			bodyStart,
			end,
		})
	}

	// TypeScript接口
	const interfaceRegex = /interface\s+\w+[^{]*\{/g
	while ((match = interfaceRegex.exec(content)) !== null) {
		const start = match.index
		const bodyStart = start + match[0].length
		const end = findMatchingBrace(content, start)
		declarations.push({
			type: "interface",
			start,
			bodyStart,
			end,
		})
	}

	// Python顶级函数 (只检测顶级，不包括嵌套函数和方法)
	const pythonDefRegex = /^def\s+\w+\s*\([^)]*\):/gm
	while ((match = pythonDefRegex.exec(content)) !== null) {
		const start = match.index
		const bodyStart = start + match[0].length
		const end = findPythonBlockEndImproved(content, start, 0)
		declarations.push({
			type: "def",
			start,
			bodyStart,
			end,
			indent: 0,
		})
	}

	// Python顶级类 (只检测顶级类)
	const pythonClassRegex = /^class\s+\w+[^:]*:/gm
	while ((match = pythonClassRegex.exec(content)) !== null) {
		const start = match.index
		const bodyStart = start + match[0].length
		const end = findPythonBlockEndImproved(content, start, 0)
		declarations.push({
			type: "class",
			start,
			bodyStart,
			end,
			indent: 0,
		})
	}

	return declarations.sort((a, b) => a.start - b.start)
}

// 查找匹配的大括号
function findMatchingBrace(content, start) {
	const openBrace = content.indexOf("{", start)
	if (openBrace === -1) {
		return content.length
	}

	let braceCount = 1
	let pos = openBrace + 1

	while (pos < content.length && braceCount > 0) {
		if (content[pos] === "{") {
			braceCount++
		} else if (content[pos] === "}") {
			braceCount--
		}
		pos++
	}

	return pos
}

// 改进的Python块结束位置查找
function findPythonBlockEndImproved(content, start, baseIndent) {
	const lines = content.split("\n")
	const startLine = content.slice(0, start).split("\n").length - 1

	let lastContentLine = startLine

	// 查找当前块的实际内容范围
	for (let i = startLine + 1; i < lines.length; i++) {
		const line = lines[i]
		const trimmedLine = line.trim()

		// 跳过空行和注释行，但记录最后一个有内容的行
		if (trimmedLine === "" || trimmedLine.startsWith("#")) {
			continue
		}

		const currentIndent = line.match(/^(\s*)/)[1].length

		// 如果找到同级或更高级别的代码，说明当前块结束了
		if (currentIndent <= baseIndent) {
			// 检查是否是顶级声明
			if (trimmedLine.match(/^(def|class|import|from)\s+/) || currentIndent === 0) {
				// 返回到上一个有内容行的结束位置
				return lines.slice(0, lastContentLine + 1).join("\n").length
			}
		}

		// 如果是当前块的内容（缩进更深），更新最后内容行
		if (currentIndent > baseIndent) {
			lastContentLine = i
		}
	}

	// 如果到文件结尾都没找到同级声明，返回最后内容行的结束位置
	return lines.slice(0, lastContentLine + 1).join("\n").length
}

// 旧的Python块结束检测函数（保留用于兼容）
function findPythonBlockEnd(content, start) {
	const lines = content.split("\n")
	const startLine = content.slice(0, start).split("\n").length - 1

	// 找到定义行的缩进级别
	const defLine = lines[startLine]
	const baseIndent = defLine.match(/^(\s*)/)[1].length

	// 查找下一个同级或更高级别的声明
	for (let i = startLine + 1; i < lines.length; i++) {
		const line = lines[i].trim()
		if (line === "") {
			continue // 跳过空行
		}

		const currentIndent = lines[i].match(/^(\s*)/)[1].length
		if (currentIndent <= baseIndent && line.match(/^(def|class)\s+/)) {
			return lines.slice(0, i).join("\n").length
		}
	}

	return content.length
}
