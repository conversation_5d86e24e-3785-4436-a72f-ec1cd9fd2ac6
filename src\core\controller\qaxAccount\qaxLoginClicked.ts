import { EmptyRequest, String } from "@shared/proto/cline/common"
import { QaxAuthService } from "@/qax/services/auth/QaxAuthService"
import { Controller } from "../index"

/**
 * Handles the user clicking the login link in the UI.
 * Generates a secure nonce for state validation, stores it in secrets,
 * and opens the authentication URL in the external browser.
 *
 * @param controller The controller instance.
 * @returns The login URL as a string.
 */
export async function qaxLoginClicked(controller: Controller, _: EmptyRequest): Promise<String> {
	return await QaxAuthService.getInstance(controller).createAuthRequest()
}
