syntax = "proto3";

package qax;
import "cline/common.proto";
import "cline/models.proto";

option java_package = "bot.qax.proto";
option java_multiple_files = true;

service QaxUtilsService {
  // Enhances a given prompt using an API provider
  rpc enhancePrompt(cline.StringRequest) returns (cline.String);
  // Fetches available models from QAX
  rpc getQaxModels(cline.OpenAiModelsRequest) returns (cline.StringArray);
  // Fetches available models from QAX Codegen
  rpc getQaxCodegenModels(cline.OpenAiModelsRequest) returns (cline.StringArray);
}
