# Qax Codegen 修改的 Cline 文件清单

本文档记录为实现 Qax Codegen 功能而修改的所有 Cline 原有文件。

## 变更文件列表
### 删除文件
- `src/standalone/standalone.ts` cline 删除

### 修改的 Cline 文件

#### proto(生成 webview 和 后端都使用， 影响 controller 生成)
- `proto/account.proto` - 账户服务
- `proto/models.proto` - api provider

#### 核心配置文件
- `src/extension.ts` - 添加 Qax Codegen 认证回调处理和 QaxAuthService 集成，扩展 URI 处理器支持 Qax Codegen 认证流程
- `src/core/storage/state.ts` - 扩展状态管理，支持 QAX 状态存储
- `src/core/storage/state-keys.ts` - 存储 key
- `src/core/webview/index.ts` 这个里面配置会影响 posthog 打点数据上报
- `src/shared/services/config/posthog-config.ts` posthog 配置

#### webview UI
```
webview-ui/src/components/account/AccountOptions.tsx
webview-ui/src/components/account/AccountView.tsx
webview-ui/src/components/chat/ChatTextArea.tsx
webview-ui/src/components/settings/ApiOptions.tsx
webview-ui/src/components/settings/ClineAccountInfoCard.tsx
webview-ui/src/components/welcome/WelcomeView.tsx
webview-ui/src/context/ClineAuthContext.tsx
webview-ui/src/utils/validate.ts
#webview-ui/src/context/ExtensionStateContext.tsx
webview-ui/src/components/settings/utils/providerUtils.ts
webview-ui/src/components/common/TelemetryBanner.tsx
```

#### controller
- `src/core/controller/index.ts` - 扩展控制器核心，qax controller 的初始化


#### API 和数据转换文件
- `src/api/index.ts` - 扩展 API 处理器构建逻辑，支持 QAX provider
- `src/shared/api.ts` - 扩展 API 类型定义，支持 QAX provider
- `src/shared/proto-conversions/models/api-configuration-conversion.ts` - 扩展 API 配置转换，支持 QAX provider 配置
- `src/shared/ExtensionMessage.ts` 扩展共享信息

### 新增的 Qax Codegen 文件
- `src/shared/qax.ts` - 提供前后端共享的 Qax Codegen 配置管理
- `src/qax/` - Qax Codegen 后端功能实现，包含服务、控制器、工具等完整目录结构
- `webview-ui/src/qax/` - Qax Codegen 前端功能实现，包含组件、上下文、服务等完整目录结构


## Qax Codegen 开发规范

### Qax Codegen 服务命名规范
> 文件名，服务等尽可能使用 Qax 前shh识别
1. 必须使用 `Qax` 前缀 + PascalCase
