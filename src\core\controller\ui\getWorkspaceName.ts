import { EmptyRequest, String } from "@shared/proto/cline/common"
import * as path from "path"
import { HostProvider } from "@/hosts/host-provider"
import type { Controller } from "../index"

/**
 * Get the current workspace name
 * @param controller The controller instance
 * @param request The empty request
 * @returns The workspace name
 */
export async function getWorkspaceName(controller: Controller, _: EmptyRequest): Promise<String> {
	try {
		// Get workspace paths using the host bridge provider
		const workspacePaths = await HostProvider.workspace.getWorkspacePaths({})

		if (!workspacePaths.paths || workspacePaths.paths.length === 0) {
			return String.create({ value: "No Workspace" })
		}

		// Get the first workspace path (primary workspace)
		const primaryWorkspacePath = workspacePaths.paths[0]
		const workspaceName = path.basename(primaryWorkspacePath)

		return String.create({ value: workspaceName })
	} catch (error) {
		console.error("Error getting workspace name:", error)
		return String.create({ value: "Unknown Workspace" })
	}
}
