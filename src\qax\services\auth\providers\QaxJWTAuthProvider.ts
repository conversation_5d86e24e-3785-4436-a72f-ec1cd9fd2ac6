// 使用 Controller 访问 CacheService 进行安全存储
import type { QaxUserInfo } from "@shared/QaxUserInfo"
import type { Controller } from "@/core/controller"
import { validateJWTToken } from "@/qax/utils/jwt"

/**
 * JWT token 的原始 payload 结构（用于解析）
 * 使用下划线命名，与 JWT 标准保持一致
 */
interface JWTPayload {
	display_name: string
	name: string
	email: string
	employee_number: string
	sub: string
	iss: string
	aud: string[]
	exp: number
	iat: number
}

/**
 * Qax JWT Authentication Provider
 * Handles JWT token-based authentication for Qax services
 */
export class QaxJWTAuthProvider {
	private _config: any
	private _currentToken: string | null = null
	private _currentUser: QaxUserInfo | null = null

	constructor(config: any) {
		this._config = config || {}
	}

	get config(): any {
		return this._config
	}

	set config(value: any) {
		this._config = value
	}

	/**
	 * Gets the current JWT authentication token
	 */
	async getAuthToken(): Promise<string | null> {
		return this._currentToken
	}

	/**
	 * Parses and validates a JWT token
	 * @param token - The JWT token to parse
	 * @returns Parsed and converted user info or null if invalid
	 */
	private parseJWTToken(token: string): QaxUserInfo | null {
		try {
			// 使用 jwt.ts 中的工具函数进行验证
			validateJWTToken(token)

			// 解析 JWT payload
			const parts = token.split(".")

			const decodedPayload = Buffer.from(parts[1], "base64url").toString("utf-8")

			const parsedPayload = JSON.parse(decodedPayload) as JWTPayload

			// Basic validation
			if (!parsedPayload.sub || !parsedPayload.email) {
				console.error("[QAX JWT] Missing required fields - sub:", !!parsedPayload.sub, "email:", !!parsedPayload.email)
				throw new Error("Invalid JWT payload: missing required fields")
			}

			// Convert to QaxUserInfo format (camelCase naming)
			return {
				sub: parsedPayload.sub,
				displayName: parsedPayload.display_name,
				name: parsedPayload.name,
				email: parsedPayload.email,
				employeeNumber: parsedPayload.employee_number,
				iss: parsedPayload.iss,
				aud: parsedPayload.aud,
				exp: parsedPayload.exp,
				iat: parsedPayload.iat,
			}
		} catch (error) {
			console.error("[QAX JWT] Failed to parse JWT token:", error)
			console.error("[QAX JWT] Token preview:", token?.substring(0, 100) + "...")
			return null
		}
	}

	/**
	 * Signs in with a JWT token
	 * @param controller - Controller instance for accessing CacheService
	 * @param token - The JWT token
	 * @returns Promise resolving to user data
	 */
	async signIn(controller: Controller, token: string): Promise<QaxUserInfo> {
		try {
			const payload = this.parseJWTToken(token)
			if (!payload) {
				throw new Error("Invalid JWT token")
			}

			// Store the token securely
			await this.storeAuthToken(controller, token)

			this._currentToken = token
			this._currentUser = payload

			return payload
		} catch (error) {
			console.error("Failed to sign in with JWT token:", error)
			throw error
		}
	}

	/**
	 * Signs out the current user
	 * @param controller - Controller instance for accessing CacheService
	 */
	async signOut(controller: Controller): Promise<void> {
		try {
			this._currentToken = null
			this._currentUser = null

			// Clear the stored token
			controller.cacheService.setSecret("qaxAccountToken", undefined)
		} catch (error) {
			console.error("Failed to sign out from QAX:", error)
			throw error
		}
	}

	/**
	 * Stores the JWT token securely
	 * @param controller - Controller instance for accessing CacheService
	 * @param token - The JWT token to store
	 */
	private async storeAuthToken(controller: Controller, token: string): Promise<void> {
		try {
			controller.cacheService.setSecret("qaxAccountToken", token)
		} catch (error) {
			console.error("Failed to store QAX JWT token:", error)
			throw error
		}
	}

	/**
	 * Restores the JWT token from storage
	 * @param controller - Controller instance for accessing CacheService
	 * @returns Promise resolving to user data or null
	 */
	async restoreAuthCredential(controller: Controller): Promise<QaxUserInfo | null> {
		try {
			const token = controller.cacheService.getSecretKey("qaxAccountToken")
			if (!token) {
				console.log("No stored QAX JWT token found.")
				return null
			}

			const payload = this.parseJWTToken(token)
			if (!payload) {
				console.error("Stored QAX JWT token is invalid or expired.")
				// Clear invalid token
				controller.cacheService.setSecret("qaxAccountToken", undefined)
				return null
			}

			this._currentToken = token
			this._currentUser = payload

			return payload
		} catch (error) {
			console.error("Failed to restore QAX JWT token:", error)
			return null
		}
	}

	/**
	 * Refreshes the authentication token (for JWT, this would typically involve
	 * getting a new token from the auth server, but for now we'll just validate the current one)
	 */
	async refreshAuthToken(): Promise<string | null> {
		if (!this._currentToken) {
			return null
		}

		// For JWT tokens, we typically don't refresh them client-side
		// Instead, we would need to redirect to the auth server for a new token
		// For now, just return the current token if it's still valid
		const payload = this.parseJWTToken(this._currentToken)
		if (payload) {
			return this._currentToken
		}

		// Token is invalid/expired, clear it
		this._currentToken = null
		this._currentUser = null
		return null
	}

	/**
	 * Gets the current user data
	 */
	getCurrentUser(): QaxUserInfo | null {
		return this._currentUser
	}
}
