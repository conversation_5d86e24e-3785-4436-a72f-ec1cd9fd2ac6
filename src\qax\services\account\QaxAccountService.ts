import type { Controller } from "@/core/controller"
import { QaxAuthService } from "../auth/QaxAuthService"

/**
 * Qax 账户服务
 * 处理认证相关的操作和请求验证
 */
export class QaxAccountService {
	private static instance: QaxAccountService
	private _authService: QaxAuthService

	constructor(controller: Controller) {
		this._authService = QaxAuthService.getInstance(controller)
	}

	/**
	 * Returns the singleton instance of QaxAccountService
	 * @param controller Controller instance (required for first initialization)
	 * @returns Singleton instance of QaxAccountService
	 */
	public static getInstance(controller?: Controller): QaxAccountService {
		if (!QaxAccountService.instance) {
			if (!controller) {
				throw new Error("Controller is required for first initialization of QaxAccountService")
			}
			QaxAccountService.instance = new QaxAccountService(controller)
		}
		return QaxAccountService.instance
	}

	/**
	 * 获取认证服务实例
	 */
	getAuthService(): QaxAuthService {
		return this._authService
	}

	/**
	 * Validates if the user has a valid JWT token for API requests.
	 * @throws Error if the user is not authenticated
	 * @returns {Promise<void>} A promise that resolves if the user is authenticated.
	 */
	async validateRequest(): Promise<void> {
		const qaxToken = await this._authService.getAuthToken()
		if (!qaxToken) {
			throw new Error("QAX authentication required. Please login to QAX Account first.")
		}
	}
}
