/**
 * Qax JWT Token 工具类
 * 提供 JWT token 的基础验证和工具函数
 * 注意：JWT 解析和用户信息提取功能已移至 QaxJWTAuthProvider
 * <AUTHOR>
 */

/**
 * JWT Token 验证错误类型
 */
export class JWTValidationError extends Error {
	constructor(
		message: string,
		public readonly code: string,
	) {
		super(message)
		this.name = "JWTValidationError"
	}
}

/**
 * 验证 JWT token 格式是否正确
 * @param token JWT token
 * @returns 是否格式正确
 */
export function isValidJWTFormat(token: string): boolean {
	if (!token || typeof token !== "string") {
		return false
	}

	// JWT 应该有三个部分，用 . 分隔
	const parts = token.split(".")
	return parts.length === 3
}

/**
 * 获取 JWT token 的过期时间
 * @param token JWT token
 * @returns 过期时间戳，如果解析失败则返回 0
 */
function getJWTExpiration(token: string): number {
	try {
		if (!isValidJWTFormat(token)) {
			return 0
		}
		const parts = token.split(".")
		// 使用 Buffer.from 进行 base64url 解码，与 QaxJWTAuthProvider 保持一致
		const decodedPayload = Buffer.from(parts[1], "base64url").toString("utf-8")
		const payload = JSON.parse(decodedPayload)
		return payload.exp || 0
	} catch (error) {
		return 0
	}
}

/**
 * 检查 JWT token 是否已过期
 * @param token JWT token
 * @returns 是否已过期
 */
export function isJWTExpired(token: string): boolean {
	try {
		const exp = getJWTExpiration(token)
		if (!exp) {
			return true
		}
		const now = Math.floor(Date.now() / 1000)
		return exp <= now
	} catch (error) {
		return true // 解析失败视为过期
	}
}

/**
 * 验证 JWT token 是否有效（格式正确且未过期）
 * @param token JWT token
 * @returns 是否有效
 */
export function isValidJWTToken(token: string): boolean {
	if (!token) {
		return false
	}

	try {
		return isValidJWTFormat(token) && !isJWTExpired(token)
	} catch (error) {
		return false
	}
}

/**
 * 验证 JWT token 并抛出详细错误信息
 * @param token JWT token
 * @throws JWTValidationError 如果 token 无效
 */
export function validateJWTToken(token: string): void {
	if (!token) {
		throw new JWTValidationError("JWT token is required", "MISSING_TOKEN")
	}

	if (!isValidJWTFormat(token)) {
		console.error("[JWT Validation] Invalid format - token parts:", token.split(".").length)
		throw new JWTValidationError("Invalid JWT token format", "INVALID_FORMAT")
	}

	if (isJWTExpired(token)) {
		const exp = getJWTExpiration(token)
		const now = Math.floor(Date.now() / 1000)
		console.error("[JWT Validation] Token expired - exp:", exp, "now:", now, "remaining:", exp - now)
		throw new JWTValidationError("JWT token has expired", "TOKEN_EXPIRED")
	}
}

/**
 * 获取 JWT token 的剩余有效时间（秒）
 * @param token JWT token
 * @returns 剩余有效时间（秒），如果已过期或无效则返回 0
 */
export function getJWTRemainingTime(token: string): number {
	try {
		const exp = getJWTExpiration(token)
		if (!exp) {
			return 0
		}
		const now = Math.floor(Date.now() / 1000)
		return Math.max(0, exp - now)
	} catch (error) {
		return 0
	}
}

/**
 * 从 VSCode URI 中提取 QAX JWT token
 * QAX 的 token 可能作为 query 参数或直接作为 query 字符串传递
 *
 * @param uri - VSCode URI 对象
 * @param query - 解析后的 URLSearchParams 对象
 * @returns 提取到的 JWT token 或 null
 */
export function extractQaxTokenFromUri(uri: { query?: string }, query: URLSearchParams): string | null {
	console.log("[QAX JWT] Extracting token from URI:", {
		uriQuery: uri.query,
		queryParams: Array.from(query.entries()),
	})

	// 首先尝试从 query 参数中获取 token
	let token = query.get("token")
	console.log("[QAX JWT] Token from query param:", token ? `${token.substring(0, 50)}...` : null)

	// 如果没有找到 token 参数，检查整个 query 字符串是否就是 token
	if (!token && uri.query) {
		// 移除可能的 fragment 部分（#null）
		const cleanQuery = uri.query.split("#")[0]
		console.log("[QAX JWT] Checking clean query as token:", cleanQuery ? `${cleanQuery.substring(0, 50)}...` : null)

		// 使用 JWT 工具验证格式
		if (cleanQuery && isValidJWTFormat(cleanQuery)) {
			token = cleanQuery
			console.log("[QAX JWT] Using clean query as token")
		}
	}

	console.log("[QAX JWT] Final extracted token:", token ? `${token.substring(0, 50)}...` : null)
	return token
}
